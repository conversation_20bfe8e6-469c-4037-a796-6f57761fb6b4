/**
 * 数据质量检查器 - 确保市场数据和技术指标的准确性
 */

import {
  MarketData,
  TechnicalIndicators,
  ProcessedKlineData,
} from "@/types/trading";
import {
  add,
  subtract,
  multiply,
  divide,
  abs,
  max,
  min,
  toBig,
  toNumber,
  gte,
  lte,
  gt,
  lt,
} from "@/lib/big-utils";

export interface DataQualityReport {
  isValid: boolean;
  score: number; // 0-100
  issues: string[];
  warnings: string[];
  recommendations: string[];
}

export class DataQualityChecker {
  private static instance: DataQualityChecker;

  static getInstance(): DataQualityChecker {
    if (!DataQualityChecker.instance) {
      DataQualityChecker.instance = new DataQualityChecker();
    }
    return DataQualityChecker.instance;
  }

  /**
   * 检查市场数据质量
   */
  checkMarketData(marketData: MarketData): DataQualityReport {
    const issues: string[] = [];
    const warnings: string[] = [];
    const recommendations: string[] = [];
    let score = 100;

    // 检查数据完整性
    const dataCompleteness = this.checkDataCompleteness(marketData);
    if (!dataCompleteness.isComplete) {
      issues.push(...dataCompleteness.issues);
      score -= 30;
    }

    // 检查数据一致性
    const dataConsistency = this.checkDataConsistency(marketData);
    if (!dataConsistency.isConsistent) {
      issues.push(...dataConsistency.issues);
      score -= 20;
    }

    // 检查数据时效性
    const dataFreshness = this.checkDataFreshness(marketData);
    if (!dataFreshness.isFresh) {
      warnings.push(...dataFreshness.warnings);
      score -= 10;
    }

    // 检查异常值
    const outliers = this.detectOutliers(marketData);
    if (outliers.hasOutliers) {
      warnings.push(...outliers.warnings);
      score -= 5;
    }

    // 检查成交量异常
    const volumeCheck = this.checkVolumeAnomalies(marketData);
    if (volumeCheck.hasAnomalies) {
      warnings.push(...volumeCheck.warnings);
      score -= 5;
    }

    // 生成建议
    if (score < 70) {
      recommendations.push("数据质量较差，建议重新获取数据");
    }
    if (issues.length > 0) {
      recommendations.push("修复数据完整性和一致性问题");
    }
    if (warnings.length > 0) {
      recommendations.push("关注数据异常，可能影响分析准确性");
    }

    return {
      isValid: score >= 60,
      score: toNumber(max(0, score)),
      issues,
      warnings,
      recommendations,
    };
  }

  /**
   * 检查技术指标质量
   */
  checkTechnicalIndicators(indicators: TechnicalIndicators): DataQualityReport {
    const issues: string[] = [];
    const warnings: string[] = [];
    const recommendations: string[] = [];
    let score = 100;

    // 检查指标有效性
    if (isNaN(indicators.rsi) || indicators.rsi < 0 || indicators.rsi > 100) {
      issues.push("RSI指标值异常");
      score -= 20;
    }

    if (isNaN(indicators.macd.macd) || isNaN(indicators.macd.signal)) {
      issues.push("MACD指标计算异常");
      score -= 20;
    }

    if (indicators.bollinger.upper <= indicators.bollinger.lower) {
      issues.push("布林带指标逻辑错误");
      score -= 15;
    }

    if (indicators.support >= indicators.resistance) {
      issues.push("支撑阻力位逻辑错误");
      score -= 15;
    }

    // 检查EMA逻辑
    if (indicators.ema.ema20 && indicators.ema.ema50 && indicators.ema.ema200) {
      const emaLogic = this.checkEMALogic(indicators.ema);
      if (!emaLogic.isValid) {
        warnings.push(...emaLogic.warnings);
        score -= 5;
      }
    }

    // 检查指标极值
    if (indicators.rsi > 95 || indicators.rsi < 5) {
      warnings.push("RSI处于极值区域，可能存在计算问题");
      score -= 5;
    }

    // 生成建议
    if (score < 70) {
      recommendations.push("技术指标质量较差，建议重新计算");
    }
    if (issues.length > 0) {
      recommendations.push("修复技术指标计算错误");
    }

    return {
      isValid: score >= 60,
      score: toNumber(max(0, score)),
      issues,
      warnings,
      recommendations,
    };
  }

  /**
   * 综合数据质量评估（增强版）
   */
  comprehensiveCheck(
    marketData: MarketData,
    indicators: TechnicalIndicators
  ): DataQualityReport {
    const marketReport = this.checkMarketData(marketData);
    const indicatorReport = this.checkTechnicalIndicators(indicators);

    // 新增：市场环境质量检查
    const marketEnvironmentReport = this.checkMarketEnvironment(marketData);

    // 新增：数据一致性交叉验证
    const crossValidationReport = this.crossValidateData(
      marketData,
      indicators
    );

    // 调整权重：市场数据40%，技术指标30%，市场环境20%，交叉验证10%
    const combinedScore =
      marketReport.score * 0.4 +
      indicatorReport.score * 0.3 +
      marketEnvironmentReport.score * 0.2 +
      crossValidationReport.score * 0.1;

    const allIssues = [
      ...marketReport.issues,
      ...indicatorReport.issues,
      ...marketEnvironmentReport.issues,
      ...crossValidationReport.issues,
    ];

    const allWarnings = [
      ...marketReport.warnings,
      ...indicatorReport.warnings,
      ...marketEnvironmentReport.warnings,
      ...crossValidationReport.warnings,
    ];

    const allRecommendations = [
      ...marketReport.recommendations,
      ...indicatorReport.recommendations,
      ...marketEnvironmentReport.recommendations,
      ...crossValidationReport.recommendations,
    ];

    // 根据数据质量调整AI分析的信心度建议
    if (combinedScore < 70) {
      allRecommendations.push("数据质量偏低，建议降低AI分析的信心度权重");
    }
    if (combinedScore < 50) {
      allRecommendations.push("数据质量严重不足，建议暂停自动交易决策");
    }

    return {
      isValid: combinedScore >= 60 && allIssues.length === 0,
      score: combinedScore,
      issues: allIssues,
      warnings: allWarnings,
      recommendations: allRecommendations,
    };
  }

  // 私有方法
  private checkDataCompleteness(marketData: MarketData): {
    isComplete: boolean;
    issues: string[];
  } {
    const issues: string[] = [];

    if (!marketData.oneMin || marketData.oneMin.length < 30) {
      issues.push("1分钟数据不足（需要至少30个数据点）");
    }

    if (!marketData.thirtyMin || marketData.thirtyMin.length < 20) {
      issues.push("30分钟数据不足（需要至少20个数据点）");
    }

    if (!marketData.hourly || marketData.hourly.length < 24) {
      issues.push("小时数据不足（需要至少24个数据点）");
    }

    if (!marketData.daily || marketData.daily.length < 14) {
      issues.push("日线数据不足（需要至少14个数据点）");
    }

    return {
      isComplete: issues.length === 0,
      issues,
    };
  }

  private checkDataConsistency(marketData: MarketData): {
    isConsistent: boolean;
    issues: string[];
  } {
    const issues: string[] = [];

    // 检查价格逻辑
    const checkPriceLogic = (data: ProcessedKlineData[], timeframe: string) => {
      for (const item of data) {
        if (item.high < item.low) {
          issues.push(`${timeframe}数据中存在最高价低于最低价的异常`);
        }
        if (item.close > item.high || item.close < item.low) {
          issues.push(`${timeframe}数据中收盘价超出高低价范围`);
        }
        if (item.open > item.high || item.open < item.low) {
          issues.push(`${timeframe}数据中开盘价超出高低价范围`);
        }
        if (item.volume < 0) {
          issues.push(`${timeframe}数据中存在负成交量`);
        }
      }
    };

    checkPriceLogic(marketData.oneMin, "1分钟");
    checkPriceLogic(marketData.thirtyMin, "30分钟");
    checkPriceLogic(marketData.hourly, "小时");
    checkPriceLogic(marketData.daily, "日线");

    return {
      isConsistent: issues.length === 0,
      issues,
    };
  }

  private checkDataFreshness(marketData: MarketData): {
    isFresh: boolean;
    warnings: string[];
  } {
    const warnings: string[] = [];
    const now = Date.now();
    const fiveMinutesAgo = now - 5 * 60 * 1000;

    // 检查最新数据的时效性
    const latestOneMin = marketData.oneMin[marketData.oneMin.length - 1];
    if (latestOneMin && latestOneMin.timestamp < fiveMinutesAgo) {
      warnings.push("1分钟数据不够新鲜，可能影响实时分析");
    }

    return {
      isFresh: warnings.length === 0,
      warnings,
    };
  }

  private detectOutliers(marketData: MarketData): {
    hasOutliers: boolean;
    warnings: string[];
  } {
    const warnings: string[] = [];

    // 检查价格异常波动
    const checkPriceOutliers = (
      data: ProcessedKlineData[],
      timeframe: string
    ) => {
      if (data.length < 10) return;

      const prices = data.map((d) => d.close);
      const mean =
        prices.reduce((sum, price) => sum + price, 0) / prices.length;
      const variance =
        prices.reduce((sum, price) => sum + Math.pow(price - mean, 2), 0) /
        prices.length;
      const stdDev = Math.sqrt(variance);

      const threshold = toNumber(multiply(3, stdDev));
      const outliers = prices.filter(
        (price) => Math.abs(price - mean) > threshold
      );

      if (outliers.length > 0) {
        warnings.push(`${timeframe}数据中发现${outliers.length}个价格异常值`);
      }
    };

    checkPriceOutliers(marketData.daily, "日线");
    checkPriceOutliers(marketData.hourly, "小时");

    return {
      hasOutliers: warnings.length > 0,
      warnings,
    };
  }

  private checkVolumeAnomalies(marketData: MarketData): {
    hasAnomalies: boolean;
    warnings: string[];
  } {
    const warnings: string[] = [];

    const dailyVolumes = marketData.daily.map((d) => d.volume);
    if (dailyVolumes.length < 7) return { hasAnomalies: false, warnings };

    const avgVolume =
      dailyVolumes.reduce((sum, vol) => sum + vol, 0) / dailyVolumes.length;
    const latestVolume = dailyVolumes[dailyVolumes.length - 1];

    // 检查成交量异常
    if (gt(latestVolume, multiply(avgVolume, 5))) {
      warnings.push("最新成交量异常放大，可能存在特殊事件");
    } else if (lt(latestVolume, multiply(avgVolume, 0.1))) {
      warnings.push("最新成交量异常萎缩，可能影响分析可靠性");
    }

    return {
      hasAnomalies: warnings.length > 0,
      warnings,
    };
  }

  private checkEMALogic(ema: {
    ema20: number;
    ema50: number;
    ema200: number;
  }): { isValid: boolean; warnings: string[] } {
    const warnings: string[] = [];

    // 在强趋势市场中，EMA应该有一定的排列关系
    const { ema20, ema50, ema200 } = ema;

    // 检查EMA值的合理性
    if (gt(divide(abs(subtract(ema20, ema50)), ema50), 0.2)) {
      warnings.push("EMA20与EMA50差距过大，可能存在计算问题");
    }

    if (gt(divide(abs(subtract(ema50, ema200)), ema200), 0.3)) {
      warnings.push("EMA50与EMA200差距过大，可能存在计算问题");
    }

    return {
      isValid: warnings.length === 0,
      warnings,
    };
  }

  /**
   * 检查市场环境质量
   */
  protected checkMarketEnvironment(marketData: MarketData): DataQualityReport {
    const issues: string[] = [];
    const warnings: string[] = [];
    const recommendations: string[] = [];
    let score = 100;

    // 检查市场流动性
    const liquidityCheck = this.checkMarketLiquidity(marketData);
    if (!liquidityCheck.isGood) {
      warnings.push(...liquidityCheck.warnings);
      score -= 5; // 降低流动性问题的扣分，因为这不是致命问题
    }

    // 检查市场波动性
    const volatilityCheck = this.checkMarketVolatility(marketData);
    if (volatilityCheck.isExtreme) {
      warnings.push("市场波动性极端，可能影响预测准确性");
      score -= 15;
    }

    // 检查交易时段
    const tradingSessionCheck = this.checkTradingSession();
    if (!tradingSessionCheck.isActive) {
      warnings.push("当前处于低活跃交易时段");
      score -= 5;
    }

    return {
      isValid: score >= 70,
      score: toNumber(max(0, score)),
      issues,
      warnings,
      recommendations,
    };
  }

  /**
   * 数据交叉验证
   */
  protected crossValidateData(
    marketData: MarketData,
    indicators: TechnicalIndicators
  ): DataQualityReport {
    const issues: string[] = [];
    const warnings: string[] = [];
    const recommendations: string[] = [];
    let score = 100;

    // 价格与技术指标一致性检查
    const priceIndicatorConsistency = this.checkPriceIndicatorConsistency(
      marketData,
      indicators
    );
    if (!priceIndicatorConsistency.isConsistent) {
      warnings.push("价格走势与技术指标存在不一致");
      score -= 20;
    }

    // 多时间框架一致性检查
    const timeframeConsistency = this.checkTimeframeConsistency(marketData);
    if (!timeframeConsistency.isConsistent) {
      warnings.push("不同时间框架数据存在矛盾");
      score -= 15;
    }

    return {
      isValid: score >= 70,
      score: toNumber(max(0, score)),
      issues,
      warnings,
      recommendations,
    };
  }

  /**
   * 检查市场流动性
   */
  private checkMarketLiquidity(marketData: MarketData): {
    isGood: boolean;
    warnings: string[];
  } {
    const warnings: string[] = [];
    const recentVolumes = marketData.oneMin.slice(-60).map((d) => d.volume); // 最近1小时
    const avgVolume =
      recentVolumes.reduce((sum, v) => sum + v, 0) / recentVolumes.length;

    // 动态调整流动性阈值，考虑不同币种的成交量差异
    const symbol = marketData.symbol;
    let volumeThreshold = 1000; // 默认阈值

    // 主流币种使用更高的阈值
    if (symbol.includes("BTC") || symbol.includes("ETH")) {
      volumeThreshold = 500; // 主流币种降低阈值
    } else {
      volumeThreshold = 100; // 其他币种进一步降低阈值
    }

    if (lt(avgVolume, volumeThreshold)) {
      warnings.push("市场流动性不足，成交量偏低");
      return { isGood: false, warnings };
    }

    return { isGood: true, warnings: [] };
  }

  /**
   * 检查市场波动性
   */
  private checkMarketVolatility(marketData: MarketData): {
    isExtreme: boolean;
  } {
    const recentPrices = marketData.oneMin.slice(-60).map((d) => d.close); // 最近1小时
    if (recentPrices.length < 10) return { isExtreme: false };

    const priceChanges = recentPrices
      .slice(1)
      .map((price, i) =>
        toNumber(
          multiply(
            abs(divide(subtract(price, recentPrices[i]), recentPrices[i])),
            100
          )
        )
      );

    const avgVolatility =
      priceChanges.reduce((sum, change) => sum + change, 0) /
      priceChanges.length;

    return { isExtreme: gt(avgVolatility, 5) }; // 5%以上波动视为极端
  }

  /**
   * 检查交易时段
   */
  private checkTradingSession(): { isActive: boolean } {
    const now = new Date();
    const hour = now.getUTCHours();

    // 主要交易时段：UTC 0-8 (亚洲), 8-16 (欧洲), 16-24 (美洲)
    // 低活跃时段：UTC 22-2
    const isLowActivity = hour >= 22 || hour <= 2;

    return { isActive: !isLowActivity };
  }

  /**
   * 检查价格与技术指标一致性
   */
  private checkPriceIndicatorConsistency(
    marketData: MarketData,
    indicators: TechnicalIndicators
  ): { isConsistent: boolean } {
    const latestPrice =
      marketData.oneMin[marketData.oneMin.length - 1]?.close || 0;

    // 检查价格与RSI的一致性
    const isRSIConsistent =
      (indicators.rsi > 70 && latestPrice > indicators.ema.ema20) ||
      (indicators.rsi < 30 && latestPrice < indicators.ema.ema20) ||
      (indicators.rsi >= 30 && indicators.rsi <= 70);

    // 检查价格与MACD的一致性
    const isMACDConsistent =
      (indicators.macd.macd > indicators.macd.signal &&
        latestPrice > indicators.ema.ema20) ||
      (indicators.macd.macd < indicators.macd.signal &&
        latestPrice < indicators.ema.ema20) ||
      lt(abs(subtract(indicators.macd.macd, indicators.macd.signal)), 0.001);

    return { isConsistent: isRSIConsistent && isMACDConsistent };
  }

  /**
   * 检查多时间框架一致性
   */
  private checkTimeframeConsistency(marketData: MarketData): {
    isConsistent: boolean;
  } {
    // 检查不同时间框架的趋势方向是否一致
    const oneMinTrend = this.calculateTrend(marketData.oneMin.slice(-15));
    const thirtyMinTrend = this.calculateTrend(marketData.thirtyMin.slice(-8));
    const hourlyTrend = this.calculateTrend(marketData.hourly.slice(-6));

    // 如果主要趋势方向冲突，认为不一致
    const trends = [oneMinTrend, thirtyMinTrend, hourlyTrend];
    const upTrends = trends.filter((t) => t > 0.5).length;
    const downTrends = trends.filter((t) => t < -0.5).length;

    // 如果有明显的趋势冲突，认为不一致
    return { isConsistent: !(upTrends > 0 && downTrends > 0) };
  }

  /**
   * 计算简单趋势
   */
  private calculateTrend(data: any[]): number {
    if (data.length < 3) return 0;

    const first = data[0].close;
    const last = data[data.length - 1].close;

    return toNumber(divide(subtract(last, first), first));
  }
}

export const dataQualityChecker = DataQualityChecker.getInstance();

/**
 * 服务端数据质量检查器 - 支持数据库存储
 */
export class ServerDataQualityChecker extends DataQualityChecker {
  private static serverInstance: ServerDataQualityChecker;

  static getServerInstance(): ServerDataQualityChecker {
    if (!ServerDataQualityChecker.serverInstance) {
      ServerDataQualityChecker.serverInstance = new ServerDataQualityChecker();
    }
    return ServerDataQualityChecker.serverInstance;
  }

  /**
   * 检查并保存数据质量记录
   */
  async checkAndSaveQuality(
    marketData: MarketData,
    indicators: TechnicalIndicators
  ): Promise<DataQualityReport> {
    const report = this.comprehensiveCheck(marketData, indicators);

    // 保存到数据库
    try {
      const { prisma } = await import("./database");
      await prisma.dataQualityRecord.create({
        data: {
          symbol: marketData.symbol,
          isValid: report.isValid,
          score: report.score,
          issues: JSON.stringify(report.issues),
          warnings: JSON.stringify(report.warnings),
          recommendations: JSON.stringify(report.recommendations),
          dataCompleteness: report.issues.length === 0,
          dataConsistency: !report.issues.some((issue) =>
            issue.includes("逻辑错误")
          ),
          dataFreshness: !report.warnings.some((warning) =>
            warning.includes("不够新鲜")
          ),
          hasOutliers: report.warnings.some((warning) =>
            warning.includes("异常值")
          ),
          volumeAnomalies: report.warnings.some((warning) =>
            warning.includes("成交量异常")
          ),
        },
      });
    } catch (error) {
      console.error("保存数据质量记录失败:", error);
    }

    return report;
  }

  /**
   * 获取历史数据质量统计
   */
  async getQualityStats(
    symbol?: string,
    days: number = 7
  ): Promise<{
    averageScore: number;
    totalChecks: number;
    validChecks: number;
    commonIssues: string[];
    trend: number;
  }> {
    try {
      const { prisma } = await import("./database");

      const since = new Date();
      since.setDate(since.getDate() - days);

      const whereClause: any = {
        timestamp: { gte: since },
      };
      if (symbol) whereClause.symbol = symbol;

      const records = await prisma.dataQualityRecord.findMany({
        where: whereClause,
        orderBy: { timestamp: "desc" },
      });

      if (records.length === 0) {
        return {
          averageScore: 0,
          totalChecks: 0,
          validChecks: 0,
          commonIssues: [],
          trend: 0,
        };
      }

      const averageScore =
        records.reduce((sum, r) => sum + r.score, 0) / records.length;
      const validChecks = records.filter((r) => r.isValid).length;

      // 分析常见问题
      const allIssues: string[] = [];
      records.forEach((r) => {
        try {
          const issues = JSON.parse(r.issues);
          allIssues.push(...issues);
        } catch (e) {
          // 忽略解析错误
        }
      });

      const issueCount: Record<string, number> = {};
      allIssues.forEach((issue) => {
        issueCount[issue] = (issueCount[issue] || 0) + 1;
      });

      const commonIssues = Object.entries(issueCount)
        .sort(([, a], [, b]) => b - a)
        .slice(0, 5)
        .map(([issue]) => issue);

      // 计算趋势
      const recentRecords = records.slice(0, Math.floor(records.length / 2));
      const olderRecords = records.slice(Math.floor(records.length / 2));

      const recentAvg =
        recentRecords.length > 0
          ? recentRecords.reduce((sum, r) => sum + r.score, 0) /
            recentRecords.length
          : 0;
      const olderAvg =
        olderRecords.length > 0
          ? olderRecords.reduce((sum, r) => sum + r.score, 0) /
            olderRecords.length
          : 0;

      const trend = recentAvg - olderAvg;

      return {
        averageScore,
        totalChecks: records.length,
        validChecks,
        commonIssues,
        trend,
      };
    } catch (error) {
      console.error("获取数据质量统计失败:", error);
      return {
        averageScore: 0,
        totalChecks: 0,
        validChecks: 0,
        commonIssues: [],
        trend: 0,
      };
    }
  }
}

export const serverDataQualityChecker =
  ServerDataQualityChecker.getServerInstance();
