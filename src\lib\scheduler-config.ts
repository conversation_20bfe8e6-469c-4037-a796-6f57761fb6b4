/**
 * 定时分析配置接口
 */
export interface SchedulerConfig {
  // 飞书配置
  feishu: {
    webhookUrl: string;
    enabled: boolean;
  };

  // 分析配置
  analysis: {
    symbols: string[];
    riskTolerance: "LOW" | "MEDIUM" | "HIGH";
    interval: number; // 分钟
    enabled: boolean;
  };

  // OpenAI配置
  openai: {
    apiKey: string;
    baseURL: string;
    model: string;
  };

  // 通知配置
  notification: {
    onlySignals: boolean; // 只在有交易信号时通知
    atAll: boolean; // 是否@所有人
    errorNotification: boolean; // 是否发送错误通知
  };
}

/**
 * 默认配置
 */
export const DEFAULT_SCHEDULER_CONFIG: SchedulerConfig = {
  feishu: {
    webhookUrl: "",
    enabled: false,
  },
  analysis: {
    symbols: ["BTCUSDT", "ETHUSDT"],
    riskTolerance: "HIGH", // 激进型
    interval: 10, // 10分钟
    enabled: true, // 默认启用定时分析
  },
  openai: {
    apiKey: "",
    baseURL: "https://api.openai.com/v1",
    model: "gpt-4",
  },
  notification: {
    onlySignals: false, // 发送所有分析结果
    atAll: false, // 是否@所有人（false=不@所有人，true=@所有人）
    errorNotification: true,
  },
};

/**
 * 配置管理服务
 * 简化版本，只从环境变量读取配置
 */
export class SchedulerConfigService {
  private static instance: SchedulerConfigService;
  private config: SchedulerConfig;

  private constructor() {
    this.config = this.loadConfig();
  }

  /**
   * 获取单例实例
   */
  static getInstance(): SchedulerConfigService {
    if (!SchedulerConfigService.instance) {
      SchedulerConfigService.instance = new SchedulerConfigService();
    }
    return SchedulerConfigService.instance;
  }

  /**
   * 从环境变量加载配置
   */
  private loadConfig(): SchedulerConfig {
    const defaultConfig = { ...DEFAULT_SCHEDULER_CONFIG };

    // 从环境变量获取OpenAI配置
    if (process.env.OPENAI_API_KEY) {
      defaultConfig.openai = {
        apiKey: process.env.OPENAI_API_KEY,
        baseURL: process.env.OPENAI_BASE_URL || defaultConfig.openai.baseURL,
        model: process.env.OPENAI_MODEL || defaultConfig.openai.model,
      };
    }

    // 从环境变量获取飞书配置
    if (process.env.FEISHU_WEBHOOK_URL) {
      defaultConfig.feishu = {
        webhookUrl: process.env.FEISHU_WEBHOOK_URL,
        enabled: true, // 如果配置了webhook URL，默认启用
      };
    }

    // 如果有必要的配置，确保定时分析启用
    if (defaultConfig.openai.apiKey) {
      defaultConfig.analysis.enabled = true;
    }

    return defaultConfig;
  }

  /**
   * 获取完整配置
   */
  getConfig(): SchedulerConfig {
    return { ...this.config };
  }

  /**
   * 验证配置
   */
  validateConfig(): { valid: boolean; errors: string[] } {
    const errors: string[] = [];

    // 验证飞书配置
    if (this.config.feishu.enabled && !this.config.feishu.webhookUrl) {
      errors.push("飞书Webhook URL不能为空");
    }

    if (
      this.config.feishu.webhookUrl &&
      !this.isValidWebhookUrl(this.config.feishu.webhookUrl)
    ) {
      errors.push("飞书Webhook URL格式不正确");
    }

    // 验证分析配置
    if (this.config.analysis.symbols.length === 0) {
      errors.push("至少需要选择一个分析币种");
    }

    if (
      this.config.analysis.interval < 1 ||
      this.config.analysis.interval > 1440
    ) {
      errors.push("分析间隔必须在1-1440分钟之间");
    }

    // 验证OpenAI配置
    if (this.config.analysis.enabled && !this.config.openai.apiKey) {
      errors.push("OpenAI API Key不能为空");
    }

    if (
      this.config.openai.baseURL &&
      !this.isValidUrl(this.config.openai.baseURL)
    ) {
      errors.push("OpenAI Base URL格式不正确");
    }

    return {
      valid: errors.length === 0,
      errors,
    };
  }

  /**
   * 验证Webhook URL格式
   */
  private isValidWebhookUrl(url: string): boolean {
    try {
      const urlObj = new URL(url);
      return (
        urlObj.hostname === "open.feishu.cn" &&
        urlObj.pathname.includes("/open-apis/bot/v2/hook/")
      );
    } catch {
      return false;
    }
  }

  /**
   * 验证URL格式
   */
  private isValidUrl(url: string): boolean {
    try {
      new URL(url);
      return true;
    } catch {
      return false;
    }
  }

  /**
   * 获取分析符号列表
   */
  getAnalysisSymbols(): string[] {
    return [...this.config.analysis.symbols];
  }

  /**
   * 检查是否启用
   */
  isEnabled(): boolean {
    return this.config.analysis.enabled && this.config.feishu.enabled;
  }

  /**
   * 获取分析间隔（毫秒）
   */
  getIntervalMs(): number {
    return this.config.analysis.interval * 60 * 1000;
  }
}

/**
 * 获取配置服务实例
 */
export const schedulerConfigService = SchedulerConfigService.getInstance();
