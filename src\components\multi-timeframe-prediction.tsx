"use client";

import React, { useState } from "react";
import {
  TrendingUp,
  TrendingDown,
  Clock,
  Target,
  AlertTriangle,
  BarChart3,
  Activity,
  Zap,
  Eye,
  ArrowUpRight,
  ArrowDownRight,
  Minus,
} from "lucide-react";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Progress } from "@/components/ui/progress";
import { Tabs, Ta<PERSON>Content, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Separator } from "@/components/ui/separator";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { MultiTimeframePrediction } from "@/types/trading";
import { formatPrice } from "@/lib/big-utils";

interface MultiTimeframePredictionProps {
  prediction: MultiTimeframePrediction;
  className?: string;
}

export function MultiTimeframePredictionComponent({
  prediction,
  className,
}: MultiTimeframePredictionProps) {
  const [activeTimeframe, setActiveTimeframe] = useState<
    "ultraShort" | "short" | "medium"
  >("ultraShort"); // 默认显示极短期预测，因为这是主要交易建议

  const getTrendIcon = (trend: string) => {
    switch (trend) {
      case "BULLISH":
        return <TrendingUp className="h-4 w-4 text-green-500" />;
      case "BEARISH":
        return <TrendingDown className="h-4 w-4 text-red-500" />;
      default:
        return <Minus className="h-4 w-4 text-gray-500" />;
    }
  };

  const getTrendColor = (trend: string) => {
    switch (trend) {
      case "BULLISH":
        return "text-green-600 bg-green-50";
      case "BEARISH":
        return "text-red-600 bg-red-50";
      default:
        return "text-gray-600 bg-gray-50";
    }
  };

  const getActionColor = (action: string) => {
    switch (action) {
      case "BUY":
        return "bg-green-500 hover:bg-green-600";
      case "SELL":
        return "bg-red-500 hover:bg-red-600";
      default:
        return "bg-gray-500 hover:bg-gray-600";
    }
  };

  const formatTimeRange = (startTime: number, endTime: number) => {
    const start = new Date(startTime);
    const end = new Date(endTime);

    const formatTime = (date: Date) => {
      return date.toLocaleString("zh-CN", {
        month: "short",
        day: "numeric",
        hour: "2-digit",
        minute: "2-digit",
      });
    };

    return `${formatTime(start)} - ${formatTime(end)}`;
  };

  const currentPrediction = prediction.predictions[activeTimeframe];

  return (
    <div className={`space-y-6 ${className}`}>
      {/* 总体评估 */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <Activity className="h-5 w-5" />
              <span>多时间段预测分析 - {prediction.symbol}</span>
            </div>
            <Badge
              className={getTrendColor(
                prediction.overallAssessment.dominantTrend
              )}
            >
              {getTrendIcon(prediction.overallAssessment.dominantTrend)}
              <span className="ml-1">
                {prediction.overallAssessment.dominantTrend === "BULLISH"
                  ? "看涨"
                  : prediction.overallAssessment.dominantTrend === "BEARISH"
                  ? "看跌"
                  : "中性"}
              </span>
            </Badge>
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* 当前价格和总体信息 */}
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="text-center">
              <p className="text-sm text-gray-500">当前价格</p>
              <p className="text-lg font-semibold">
                ${formatPrice(prediction.currentPrice)}
              </p>
            </div>
            <div className="text-center">
              <p className="text-sm text-gray-500">时间段一致性</p>
              <div className="flex items-center justify-center space-x-2">
                <Progress
                  value={prediction.correlationAnalysis.timeframeAlignment}
                  className="w-16"
                />
                <span className="text-sm font-medium">
                  {prediction.correlationAnalysis.timeframeAlignment}%
                </span>
              </div>
            </div>
            <div className="text-center">
              <p className="text-sm text-gray-500">趋势强度</p>
              <div className="flex items-center justify-center space-x-2">
                <Progress
                  value={prediction.correlationAnalysis.trendStrength}
                  className="w-16"
                />
                <span className="text-sm font-medium">
                  {prediction.correlationAnalysis.trendStrength}%
                </span>
              </div>
            </div>
            <div className="text-center">
              <p className="text-sm text-gray-500">反转概率</p>
              <div className="flex items-center justify-center space-x-2">
                <Progress
                  value={prediction.correlationAnalysis.reversal_probability}
                  className="w-16"
                />
                <span className="text-sm font-medium">
                  {prediction.correlationAnalysis.reversal_probability}%
                </span>
              </div>
            </div>
          </div>

          {/* 总体建议 */}
          <div className="bg-blue-50 p-3 rounded-lg">
            <p className="text-sm text-blue-800">
              {prediction.overallAssessment.recommendation}
            </p>
          </div>

          {/* 冲突信号警告 */}
          {prediction.overallAssessment.conflictingSignals && (
            <Alert>
              <AlertTriangle className="h-4 w-4" />
              <AlertDescription>
                <strong>注意：</strong>
                检测到不同时间段存在冲突信号，建议谨慎操作，等待更明确的方向确认。
              </AlertDescription>
            </Alert>
          )}
        </CardContent>
      </Card>

      {/* 时间段选择和详细预测 */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Clock className="h-5 w-5" />
            <span>分时段预测详情</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <Tabs
            value={activeTimeframe}
            onValueChange={(value) => setActiveTimeframe(value as any)}
          >
            <TabsList className="grid w-full grid-cols-3">
              <TabsTrigger
                value="ultraShort"
                className="flex items-center space-x-2"
              >
                <Zap className="h-4 w-4" />
                <span>极短期 (15m-2h)</span>
              </TabsTrigger>
              <TabsTrigger
                value="short"
                className="flex items-center space-x-2"
              >
                <BarChart3 className="h-4 w-4" />
                <span>短期 (2-8h)</span>
              </TabsTrigger>
              <TabsTrigger
                value="medium"
                className="flex items-center space-x-2"
              >
                <Eye className="h-4 w-4" />
                <span>中期 (8-24h)</span>
              </TabsTrigger>
            </TabsList>

            <TabsContent value={activeTimeframe} className="mt-6 space-y-6">
              {/* 时间范围显示 */}
              <div className="bg-blue-50 p-4 rounded-lg">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <Clock className="h-4 w-4 text-blue-600" />
                    <span className="text-sm font-medium text-blue-800">
                      预测时间范围
                    </span>
                  </div>
                  <div className="text-sm text-blue-700">
                    {formatTimeRange(
                      currentPrediction.startTime,
                      currentPrediction.endTime
                    )}
                  </div>
                </div>
                <div className="mt-2 text-xs text-blue-600">
                  基准时间:{" "}
                  {new Date(currentPrediction.startTime).toLocaleString(
                    "zh-CN"
                  )}{" "}
                  (当前时间)
                </div>
              </div>

              {/* 预测概览 */}
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                <div className="text-center">
                  <p className="text-sm text-gray-500">趋势方向</p>
                  <div className="flex items-center justify-center space-x-1">
                    {getTrendIcon(currentPrediction.trend)}
                    <span className="font-medium">
                      {currentPrediction.trend === "BULLISH"
                        ? "看涨"
                        : currentPrediction.trend === "BEARISH"
                        ? "看跌"
                        : "中性"}
                    </span>
                  </div>
                </div>
                <div className="text-center">
                  <p className="text-sm text-gray-500">信心度</p>
                  <div className="flex items-center justify-center space-x-2">
                    <Progress
                      value={currentPrediction.confidence}
                      className="w-16"
                    />
                    <span className="text-sm font-medium">
                      {currentPrediction.confidence}%
                    </span>
                  </div>
                </div>
                <div className="text-center">
                  <p className="text-sm text-gray-500">预期价格</p>
                  <p className="font-semibold">
                    ${formatPrice(currentPrediction.targetPrice.most_likely)}
                  </p>
                </div>
                <div className="text-center">
                  <p className="text-sm text-gray-500">建议操作</p>
                  <Badge
                    className={`${getActionColor(
                      currentPrediction.tradingStrategy.action
                    )} text-white`}
                  >
                    {currentPrediction.tradingStrategy.action === "BUY"
                      ? "买入"
                      : currentPrediction.tradingStrategy.action === "SELL"
                      ? "卖出"
                      : "观望"}
                  </Badge>
                </div>
              </div>

              <Separator />

              {/* 价格目标区间 */}
              <div>
                <h4 className="font-medium mb-3 flex items-center space-x-2">
                  <Target className="h-4 w-4" />
                  <span>价格目标区间</span>
                </h4>
                <div className="grid grid-cols-3 gap-4">
                  <div className="text-center p-3 bg-red-50 rounded-lg">
                    <p className="text-sm text-gray-500">预期最低</p>
                    <p className="font-semibold text-red-600">
                      ${formatPrice(currentPrediction.targetPrice.low)}
                    </p>
                  </div>
                  <div className="text-center p-3 bg-blue-50 rounded-lg">
                    <p className="text-sm text-gray-500">最可能价格</p>
                    <p className="font-semibold text-blue-600">
                      ${formatPrice(currentPrediction.targetPrice.most_likely)}
                    </p>
                  </div>
                  <div className="text-center p-3 bg-green-50 rounded-lg">
                    <p className="text-sm text-gray-500">预期最高</p>
                    <p className="font-semibold text-green-600">
                      ${formatPrice(currentPrediction.targetPrice.high)}
                    </p>
                  </div>
                </div>
              </div>

              <Separator />

              {/* 交易策略 */}
              <div>
                <h4 className="font-medium mb-3 flex items-center space-x-2">
                  <BarChart3 className="h-4 w-4" />
                  <span>交易策略建议</span>
                </h4>
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-4">
                  <div className="text-center">
                    <p className="text-sm text-gray-500">入场价格</p>
                    <p className="font-semibold">
                      $
                      {formatPrice(
                        currentPrediction.tradingStrategy.entryPrice
                      )}
                    </p>
                  </div>
                  <div className="text-center">
                    <p className="text-sm text-gray-500">止损价格</p>
                    <p className="font-semibold text-red-500">
                      ${formatPrice(currentPrediction.tradingStrategy.stopLoss)}
                    </p>
                  </div>
                  <div className="text-center">
                    <p className="text-sm text-gray-500">止盈目标</p>
                    <p className="font-semibold text-green-500">
                      $
                      {formatPrice(
                        currentPrediction.tradingStrategy.takeProfit[0] || 0
                      )}
                    </p>
                  </div>
                  <div className="text-center">
                    <p className="text-sm text-gray-500">建议仓位</p>
                    <p className="font-semibold">
                      {currentPrediction.tradingStrategy.positionSize}%
                    </p>
                  </div>
                </div>
                <div className="bg-gray-50 p-3 rounded-lg">
                  <p className="text-sm text-gray-700">
                    {currentPrediction.tradingStrategy.reasoning}
                  </p>
                </div>
              </div>

              <Separator />

              {/* 关键事件 */}
              {currentPrediction.keyEvents.length > 0 && (
                <div>
                  <h4 className="font-medium mb-3 flex items-center space-x-2">
                    <AlertTriangle className="h-4 w-4" />
                    <span>关键事件预测</span>
                  </h4>
                  <div className="space-y-2">
                    {currentPrediction.keyEvents.map((event, index) => (
                      <div
                        key={index}
                        className="flex items-center space-x-2 p-2 bg-blue-50 rounded"
                      >
                        <ArrowUpRight className="h-4 w-4 text-blue-500" />
                        <span className="text-sm text-blue-800">{event}</span>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {/* 风险因素和机会点 */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {/* 风险因素 */}
                {currentPrediction.riskFactors.length > 0 && (
                  <div>
                    <h4 className="font-medium mb-3 flex items-center space-x-2">
                      <AlertTriangle className="h-4 w-4 text-red-500" />
                      <span>风险因素</span>
                    </h4>
                    <div className="space-y-2">
                      {currentPrediction.riskFactors.map((risk, index) => (
                        <div
                          key={index}
                          className="flex items-start space-x-2 p-2 bg-red-50 rounded"
                        >
                          <ArrowDownRight className="h-4 w-4 text-red-500 mt-0.5" />
                          <span className="text-sm text-red-800">{risk}</span>
                        </div>
                      ))}
                    </div>
                  </div>
                )}

                {/* 机会点 */}
                {currentPrediction.opportunities.length > 0 && (
                  <div>
                    <h4 className="font-medium mb-3 flex items-center space-x-2">
                      <Target className="h-4 w-4 text-green-500" />
                      <span>交易机会</span>
                    </h4>
                    <div className="space-y-2">
                      {currentPrediction.opportunities.map(
                        (opportunity, index) => (
                          <div
                            key={index}
                            className="flex items-start space-x-2 p-2 bg-green-50 rounded"
                          >
                            <ArrowUpRight className="h-4 w-4 text-green-500 mt-0.5" />
                            <span className="text-sm text-green-800">
                              {opportunity}
                            </span>
                          </div>
                        )
                      )}
                    </div>
                  </div>
                )}
              </div>

              {/* 市场条件 */}
              <div>
                <h4 className="font-medium mb-3 flex items-center space-x-2">
                  <Activity className="h-4 w-4" />
                  <span>市场条件评估</span>
                </h4>
                <div className="grid grid-cols-3 gap-4">
                  <div className="text-center p-3 bg-gray-50 rounded-lg">
                    <p className="text-sm text-gray-500">波动率</p>
                    <Badge
                      variant={
                        currentPrediction.marketConditions.volatility === "HIGH"
                          ? "destructive"
                          : currentPrediction.marketConditions.volatility ===
                            "MEDIUM"
                          ? "default"
                          : "secondary"
                      }
                    >
                      {currentPrediction.marketConditions.volatility === "HIGH"
                        ? "高"
                        : currentPrediction.marketConditions.volatility ===
                          "MEDIUM"
                        ? "中"
                        : "低"}
                    </Badge>
                  </div>
                  <div className="text-center p-3 bg-gray-50 rounded-lg">
                    <p className="text-sm text-gray-500">成交量</p>
                    <Badge
                      variant={
                        currentPrediction.marketConditions.volume === "HIGH"
                          ? "destructive"
                          : currentPrediction.marketConditions.volume ===
                            "MEDIUM"
                          ? "default"
                          : "secondary"
                      }
                    >
                      {currentPrediction.marketConditions.volume === "HIGH"
                        ? "高"
                        : currentPrediction.marketConditions.volume === "MEDIUM"
                        ? "中"
                        : "低"}
                    </Badge>
                  </div>
                  <div className="text-center p-3 bg-gray-50 rounded-lg">
                    <p className="text-sm text-gray-500">动量</p>
                    <Badge
                      variant={
                        currentPrediction.marketConditions.momentum === "STRONG"
                          ? "destructive"
                          : currentPrediction.marketConditions.momentum ===
                            "WEAK"
                          ? "secondary"
                          : "default"
                      }
                    >
                      {currentPrediction.marketConditions.momentum === "STRONG"
                        ? "强"
                        : currentPrediction.marketConditions.momentum === "WEAK"
                        ? "弱"
                        : "中性"}
                    </Badge>
                  </div>
                </div>
              </div>
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>

      {/* 风险提示 */}
      <Alert>
        <AlertTriangle className="h-4 w-4" />
        <AlertDescription>
          <strong>风险提示：</strong>
          多时间段预测基于当前市场数据和技术分析，市场变化可能导致预测偏差。请结合实时市场情况和个人风险承受能力进行决策。
          风险等级：
          <Badge
            className={`ml-1 ${
              prediction.overallAssessment.riskLevel === "HIGH"
                ? "bg-red-500"
                : prediction.overallAssessment.riskLevel === "MEDIUM"
                ? "bg-yellow-500"
                : "bg-green-500"
            } text-white`}
          >
            {prediction.overallAssessment.riskLevel === "HIGH"
              ? "高风险"
              : prediction.overallAssessment.riskLevel === "MEDIUM"
              ? "中等风险"
              : "低风险"}
          </Badge>
        </AlertDescription>
      </Alert>
    </div>
  );
}
