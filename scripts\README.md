# AI-B 定时分析 Cron 脚本

这个独立的 Cron 脚本用于每 10 分钟自动执行 BTC 和 ETH 的激进型分析，并将结果发送到飞书。

## 功能特性

- ✅ 启动时立即执行一次分析
- ✅ 每 10 分钟自动执行一次分析
- ✅ 固定分析 BTC 和 ETH（激进型策略）
- ✅ 自动发送结果到飞书 webhooks
- ✅ **预测验证报告**: 自动验证历史预测并发送准确率报告到飞书
- ✅ 完整的错误处理和日志记录
- ✅ 优雅的启动和关闭
- ✅ API 服务状态检查

## 使用方法

### 1. 启动定时任务

```bash
# 启动定时任务（会在启动时立即执行一次分析）
npm run cron

# 或者直接运行
node scripts/cron.js
```

### 2. 检查 API 服务状态

```bash
# 检查API服务是否可用
npm run cron:check
```

### 3. 测试预测验证报告

```bash
# 测试验证报告功能
npm run test:validation
```

## 环境变量配置

可以通过环境变量自定义配置：

```bash
# API 基础URL（默认: http://localhost:3000）
export API_BASE_URL=http://localhost:3000

# 然后启动
npm run cron
```

## 前置条件

在启动 Cron 脚本之前，请确保：

1. **Next.js 应用正在运行**

   ```bash
   npm run dev
   # 或
   npm run build && npm run start
   ```

2. **配置已完成**

   - OpenAI API Key 已配置
   - 飞书 Webhook URL 已配置（可选）
   - 定时分析已启用

3. **API 接口可访问**
   - `/api/execute-analysis` 接口正常工作

## 日志说明

脚本运行时会输出详细的日志信息：

```
🚀 AI-B 定时分析 Cron 脚本启动
📍 API地址: http://localhost:3000
⏰ 执行频率: 每10分钟
🌍 时区: Asia/Shanghai

🔍 检查API服务状态...
✅ API服务状态正常
  配置信息: { analysisEnabled: true, symbols: ['BTCUSDT', 'ETHUSDT'], ... }

⏰ 启动定时任务...
✅ 定时任务已启动
📝 日志格式: [时间戳] 状态 消息

🔄 启动时执行一次分析...
[2024-06-14T10:00:00.000Z] 开始执行定时分析...
[2024-06-14T10:00:01.000Z] 🔍 开始验证历史预测...
[2024-06-14T10:00:02.000Z] ✅ 预测验证完成: 批量验证完成，验证了 3 个预测
[2024-06-14T10:00:05.000Z] ✅ 定时分析执行成功: 定时分析执行完成，成功: 2/2
  ✅ BTCUSDT: 分析完成
  ✅ ETHUSDT: 分析完成

🔄 定时任务运行中... (按 Ctrl+C 停止)

[2024-06-14T10:10:00.000Z] 开始执行定时分析...
[2024-06-14T10:10:01.000Z] 🔍 开始验证历史预测...
[2024-06-14T10:10:02.000Z] ✅ 预测验证完成: 批量验证完成，验证了 2 个预测
[2024-06-14T10:10:05.000Z] ✅ 定时分析执行成功: 定时分析执行完成，成功: 2/2
  ✅ BTCUSDT: 分析完成
  ✅ ETHUSDT: 分析完成
```

## 停止脚本

按 `Ctrl+C` 或发送 SIGINT/SIGTERM 信号来优雅地停止脚本：

```
🛑 收到停止信号，正在关闭定时任务...
✅ 定时任务已停止
```

## 错误处理

脚本包含完整的错误处理：

- **API 服务不可用**: 脚本会退出并提示检查服务状态
- **网络错误**: 记录详细的错误信息并继续运行
- **配置错误**: 显示具体的配置问题
- **分析失败**: 记录失败的币种和原因

## 预测验证报告

每次定时任务执行时，会先验证历史预测的准确性，然后将验证报告发送到飞书群。

### 报告内容

- **验证概况**: 本次验证数量、总预测数、已验证数
- **准确率统计**: 方向准确率、价格准确率、综合准确率
- **分类统计**: 按币种、时间框架、信心度的准确率分析
- **趋势分析**: 最近准确率的变化趋势
- **改进建议**: 基于统计数据生成的具体改进建议

### 执行流程

```
定时任务启动
    ↓
验证历史预测 → 计算准确率 → 生成报告 → 发送到飞书
    ↓
执行新的AI分析 → 生成交易建议 → 发送到飞书
```

详细文档请参考: [预测验证报告功能](../docs/prediction-validation-report.md)

## 技术细节

- **执行频率**: 每 10 分钟 (`*/10 * * * *`)
- **时区**: Asia/Shanghai
- **超时时间**: 5 分钟
- **分析类型**: 激进型 (HIGH risk tolerance)
- **分析币种**: BTCUSDT, ETHUSDT

## 与应用的关系

- 这个脚本是**独立运行**的，不依赖于 Next.js 应用内的定时任务
- 应用启动时**不会自动启动**定时任务
- 需要**手动启动**这个 Cron 脚本来执行定时分析
- 脚本通过 HTTP API 与应用通信

## 故障排除

### 1. 脚本无法启动

检查 API 服务是否运行：

```bash
curl http://localhost:3000/api/execute-analysis
```

### 2. 分析执行失败

检查应用日志和配置：

- OpenAI API Key 是否正确
- 网络连接是否正常
- 币安 API 是否可访问

### 3. 飞书通知未发送

检查飞书配置：

- Webhook URL 是否正确
- 飞书机器人是否启用

## 生产环境部署

在生产环境中，建议使用进程管理器：

```bash
# 使用 PM2
pm2 start scripts/cron.js --name "ai-b-cron"

# 使用 systemd
# 创建 /etc/systemd/system/ai-b-cron.service
```
