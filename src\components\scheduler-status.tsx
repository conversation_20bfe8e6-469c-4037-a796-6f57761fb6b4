"use client";

import React, { useState, useEffect } from "react";
import { Clock, CheckCircle, AlertCircle, Loader2 } from "lucide-react";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";

interface SchedulerStatus {
  analysisEnabled: boolean;
  symbols: string[];
  riskTolerance: string;
  feishuEnabled: boolean;
  openaiConfigured: boolean;
  feishuConfigured: boolean;
}

/**
 * 定时分析状态显示组件
 * 只显示状态，不提供控制功能
 */
export function SchedulerStatus() {
  const [status, setStatus] = useState<SchedulerStatus | null>(null);
  const [loading, setLoading] = useState(true);

  // 获取状态
  const fetchStatus = async () => {
    try {
      const response = await fetch("/api/execute-analysis");
      const result = await response.json();

      if (result.success) {
        setStatus(result.data);
      } else {
        console.error("获取定时分析状态失败:", result.error);
      }
    } catch (error) {
      console.error("获取定时分析状态失败:", error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchStatus();
    // 每30秒刷新一次状态
    const interval = setInterval(fetchStatus, 30000);
    return () => clearInterval(interval);
  }, []);

  if (loading) {
    return (
      <Card>
        <CardContent className="flex items-center justify-center py-8">
          <Loader2 className="h-6 w-6 animate-spin" />
        </CardContent>
      </Card>
    );
  }

  if (!status) {
    return null;
  }

  const getRiskToleranceLabel = (risk: string) => {
    switch (risk) {
      case "HIGH":
        return "激进型";
      case "MEDIUM":
        return "平衡型";
      case "LOW":
        return "保守型";
      default:
        return risk;
    }
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <Clock className="h-5 w-5" />
            <span>定时分析状态</span>
          </div>
          <div className="flex items-center space-x-2">
            <Badge className="bg-blue-100 text-blue-800">
              <div className="w-2 h-2 bg-blue-500 rounded-full mr-1"></div>
              外部Cron管理
            </Badge>
          </div>
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* 配置状态 */}
        <div className="grid grid-cols-2 gap-4">
          <div className="flex items-center space-x-2">
            {status.openaiConfigured ? (
              <CheckCircle className="h-4 w-4 text-green-500" />
            ) : (
              <AlertCircle className="h-4 w-4 text-red-500" />
            )}
            <span className="text-sm">
              OpenAI API: {status.openaiConfigured ? "已配置" : "未配置"}
            </span>
          </div>
          <div className="flex items-center space-x-2">
            {status.feishuConfigured ? (
              <CheckCircle className="h-4 w-4 text-green-500" />
            ) : (
              <AlertCircle className="h-4 w-4 text-orange-500" />
            )}
            <span className="text-sm">
              飞书通知: {status.feishuConfigured ? "已配置" : "未配置"}
            </span>
          </div>
        </div>

        {/* 分析配置 */}
        <div className="grid grid-cols-2 gap-4 text-sm">
          <div>
            <span className="text-gray-600">分析币种:</span>
            <span className="ml-2 font-medium">
              {status.symbols.join(", ")}
            </span>
          </div>
          <div>
            <span className="text-gray-600">分析间隔:</span>
            <span className="ml-2 font-medium">10分钟</span>
          </div>
          <div>
            <span className="text-gray-600">风险偏好:</span>
            <span className="ml-2 font-medium">
              {getRiskToleranceLabel(status.riskTolerance)}
            </span>
          </div>
          <div>
            <span className="text-gray-600">执行方式:</span>
            <span className="ml-2 font-medium">外部Cron脚本</span>
          </div>
        </div>

        {/* 状态说明 */}
        <div className="mt-4 p-3 bg-blue-50 rounded-lg">
          <p className="text-sm text-blue-700">
            {status.analysisEnabled
              ? "✅ 定时分析已配置，通过外部Cron脚本每10分钟执行BTC和ETH的激进型分析并发送到飞书"
              : "❌ 定时分析未启用，请检查配置"}
          </p>
        </div>
      </CardContent>
    </Card>
  );
}
