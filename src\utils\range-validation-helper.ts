/**
 * 范围验证辅助工具
 * 提供便捷的方法来创建和管理范围验证预测
 */

import { PredictionData } from "../lib/server-prediction-tracker";
import { getRecommendedConfig, getConfigByTradingType, validateConfig, RangeValidationConfig } from "../config/range-validation-config";

export interface RangeValidatedPredictionInput {
  symbol: string;
  direction: "BUY" | "SELL" | "HOLD";
  entryPrice: number;
  targetPrice: number;
  stopLoss: number;
  timeframe: string;
  confidence: number;
  analysis?: string;
  indicators?: string[];
  tradingType?: 'scalping' | 'day' | 'swing' | 'position';
  customRangeConfig?: Partial<RangeValidationConfig>;
}

/**
 * 创建带有范围验证的预测数据
 */
export function createRangeValidatedPrediction(input: RangeValidatedPredictionInput): PredictionData {
  // 获取推荐配置
  let rangeConfig: RangeValidationConfig;
  
  if (input.customRangeConfig) {
    // 使用自定义配置
    rangeConfig = {
      ...getRecommendedConfig(input.timeframe),
      ...input.customRangeConfig
    };
  } else if (input.tradingType) {
    // 根据交易类型获取配置
    rangeConfig = getConfigByTradingType(input.tradingType);
  } else {
    // 根据时间框架获取推荐配置
    rangeConfig = getRecommendedConfig(input.timeframe);
  }
  
  // 验证配置
  const validation = validateConfig(rangeConfig);
  if (!validation.valid) {
    console.warn("范围验证配置警告:", validation.errors);
  }
  
  // 构建reasoning对象
  const reasoningData = {
    analysis: input.analysis || "AI生成的交易分析",
    indicators: input.indicators || [],
    priceRangeValidation: rangeConfig,
    metadata: {
      createdAt: new Date().toISOString(),
      tradingType: input.tradingType,
      configSource: input.customRangeConfig ? "custom" : (input.tradingType ? "tradingType" : "timeframe")
    }
  };
  
  return {
    symbol: input.symbol,
    direction: input.direction,
    entryPrice: input.entryPrice,
    targetPrice: input.targetPrice,
    stopLoss: input.stopLoss,
    timeframe: input.timeframe,
    confidence: input.confidence,
    reasoning: JSON.stringify(reasoningData),
    priceRangeValidation: rangeConfig
  };
}

/**
 * 快速创建极短期范围验证预测
 */
export function createScalpingPrediction(
  symbol: string,
  direction: "BUY" | "SELL",
  entryPrice: number,
  targetPercent: number,
  stopLossPercent: number,
  confidence: number = 70
): PredictionData {
  const targetPrice = direction === "BUY" 
    ? entryPrice * (1 + targetPercent / 100)
    : entryPrice * (1 - targetPercent / 100);
    
  const stopLoss = direction === "BUY"
    ? entryPrice * (1 - stopLossPercent / 100)
    : entryPrice * (1 + stopLossPercent / 100);
  
  return createRangeValidatedPrediction({
    symbol,
    direction,
    entryPrice,
    targetPrice,
    stopLoss,
    timeframe: "15分钟到2小时",
    confidence,
    analysis: `极短期${direction}信号，目标${targetPercent}%，止损${stopLossPercent}%`,
    tradingType: 'scalping'
  });
}

/**
 * 快速创建日内交易范围验证预测
 */
export function createDayTradingPrediction(
  symbol: string,
  direction: "BUY" | "SELL",
  entryPrice: number,
  targetPercent: number,
  stopLossPercent: number,
  timeframe: string = "4小时",
  confidence: number = 75
): PredictionData {
  const targetPrice = direction === "BUY" 
    ? entryPrice * (1 + targetPercent / 100)
    : entryPrice * (1 - targetPercent / 100);
    
  const stopLoss = direction === "BUY"
    ? entryPrice * (1 - stopLossPercent / 100)
    : entryPrice * (1 + stopLossPercent / 100);
  
  return createRangeValidatedPrediction({
    symbol,
    direction,
    entryPrice,
    targetPrice,
    stopLoss,
    timeframe,
    confidence,
    analysis: `日内交易${direction}信号，目标${targetPercent}%，止损${stopLossPercent}%`,
    tradingType: 'day'
  });
}

/**
 * 批量创建多时间框架预测
 */
export function createMultiTimeframePredictions(
  symbol: string,
  direction: "BUY" | "SELL",
  entryPrice: number,
  baseTargetPercent: number,
  baseStopLossPercent: number,
  confidence: number = 75
): PredictionData[] {
  const timeframes = [
    { name: "15分钟", multiplier: 0.3, type: 'scalping' as const },
    { name: "1小时", multiplier: 0.7, type: 'scalping' as const },
    { name: "4小时", multiplier: 1.0, type: 'day' as const },
    { name: "8小时", multiplier: 1.5, type: 'swing' as const }
  ];
  
  return timeframes.map(tf => {
    const targetPercent = baseTargetPercent * tf.multiplier;
    const stopLossPercent = baseStopLossPercent * tf.multiplier;
    
    const targetPrice = direction === "BUY" 
      ? entryPrice * (1 + targetPercent / 100)
      : entryPrice * (1 - targetPercent / 100);
      
    const stopLoss = direction === "BUY"
      ? entryPrice * (1 - stopLossPercent / 100)
      : entryPrice * (1 + stopLossPercent / 100);
    
    return createRangeValidatedPrediction({
      symbol,
      direction,
      entryPrice,
      targetPrice,
      stopLoss,
      timeframe: tf.name,
      confidence,
      analysis: `多时间框架分析 - ${tf.name}级别${direction}信号`,
      tradingType: tf.type
    });
  });
}

/**
 * 验证预测参数的合理性
 */
export function validatePredictionParams(
  direction: "BUY" | "SELL",
  entryPrice: number,
  targetPrice: number,
  stopLoss: number
): { valid: boolean; errors: string[]; warnings: string[] } {
  const errors: string[] = [];
  const warnings: string[] = [];
  
  // 基础验证
  if (entryPrice <= 0) {
    errors.push("入场价格必须大于0");
  }
  
  if (targetPrice <= 0) {
    errors.push("目标价格必须大于0");
  }
  
  if (stopLoss <= 0) {
    errors.push("止损价格必须大于0");
  }
  
  // 方向逻辑验证
  if (direction === "BUY") {
    if (targetPrice <= entryPrice) {
      errors.push("BUY方向：目标价格应该高于入场价格");
    }
    if (stopLoss >= entryPrice) {
      errors.push("BUY方向：止损价格应该低于入场价格");
    }
  } else if (direction === "SELL") {
    if (targetPrice >= entryPrice) {
      errors.push("SELL方向：目标价格应该低于入场价格");
    }
    if (stopLoss <= entryPrice) {
      errors.push("SELL方向：止损价格应该高于入场价格");
    }
  }
  
  // 风险收益比验证
  if (errors.length === 0) {
    const targetDistance = Math.abs(targetPrice - entryPrice);
    const stopLossDistance = Math.abs(stopLoss - entryPrice);
    const riskRewardRatio = targetDistance / stopLossDistance;
    
    if (riskRewardRatio < 1) {
      warnings.push(`风险收益比偏低 (${riskRewardRatio.toFixed(2)}:1)，建议至少1:1`);
    }
    
    if (riskRewardRatio > 10) {
      warnings.push(`风险收益比过高 (${riskRewardRatio.toFixed(2)}:1)，可能不现实`);
    }
    
    // 价格变动幅度检查
    const targetChangePercent = Math.abs((targetPrice - entryPrice) / entryPrice) * 100;
    const stopLossChangePercent = Math.abs((stopLoss - entryPrice) / entryPrice) * 100;
    
    if (targetChangePercent > 20) {
      warnings.push(`目标价格变动幅度过大 (${targetChangePercent.toFixed(1)}%)，考虑降低预期`);
    }
    
    if (stopLossChangePercent > 10) {
      warnings.push(`止损幅度过大 (${stopLossChangePercent.toFixed(1)}%)，风险较高`);
    }
  }
  
  return {
    valid: errors.length === 0,
    errors,
    warnings
  };
}

/**
 * 计算推荐的目标价格和止损价格
 */
export function calculateRecommendedPrices(
  direction: "BUY" | "SELL",
  entryPrice: number,
  riskRewardRatio: number = 2,
  riskPercent: number = 2
): { targetPrice: number; stopLoss: number; targetPercent: number; stopLossPercent: number } {
  const stopLossPercent = riskPercent;
  const targetPercent = riskPercent * riskRewardRatio;
  
  let targetPrice: number;
  let stopLoss: number;
  
  if (direction === "BUY") {
    targetPrice = entryPrice * (1 + targetPercent / 100);
    stopLoss = entryPrice * (1 - stopLossPercent / 100);
  } else {
    targetPrice = entryPrice * (1 - targetPercent / 100);
    stopLoss = entryPrice * (1 + stopLossPercent / 100);
  }
  
  return {
    targetPrice,
    stopLoss,
    targetPercent,
    stopLossPercent
  };
}

export default {
  createRangeValidatedPrediction,
  createScalpingPrediction,
  createDayTradingPrediction,
  createMultiTimeframePredictions,
  validatePredictionParams,
  calculateRecommendedPrices
};
