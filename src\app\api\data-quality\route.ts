/**
 * 数据质量检查API接口
 */

import { NextRequest, NextResponse } from 'next/server'
import { serverDataQualityChecker } from '@/lib/data-quality-checker'
import { dataService } from '@/lib/data-service'
import { initializeDatabase } from '@/lib/database'

export async function POST(request: NextRequest) {
  try {
    // 初始化数据库连接
    await initializeDatabase()

    const body = await request.json()
    const { symbol } = body

    if (!symbol) {
      return NextResponse.json({
        success: false,
        error: '缺少币种参数'
      }, { status: 400 })
    }

    console.log(`🔍 检查 ${symbol} 的数据质量...`)

    // 获取市场数据
    const marketData = await dataService.getMarketData(symbol)
    
    // 计算技术指标
    const technicalIndicators = dataService.calculateTechnicalIndicators(marketData.oneMin)
    
    // 检查并保存数据质量
    const qualityReport = await serverDataQualityChecker.checkAndSaveQuality(
      marketData, 
      technicalIndicators
    )

    console.log(`✅ ${symbol} 数据质量检查完成，评分: ${qualityReport.score.toFixed(1)}`)

    return NextResponse.json({
      success: true,
      data: qualityReport
    })
  } catch (error) {
    console.error('数据质量检查失败:', error)
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : '检查失败'
    }, { status: 500 })
  }
}

export async function GET(request: NextRequest) {
  try {
    // 初始化数据库连接
    await initializeDatabase()

    const { searchParams } = new URL(request.url)
    const symbol = searchParams.get('symbol') || undefined
    const days = parseInt(searchParams.get('days') || '7')

    // 获取数据质量统计
    const stats = await serverDataQualityChecker.getQualityStats(symbol, days)

    return NextResponse.json({
      success: true,
      data: stats
    })
  } catch (error) {
    console.error('获取数据质量统计失败:', error)
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : '获取统计失败'
    }, { status: 500 })
  }
}
