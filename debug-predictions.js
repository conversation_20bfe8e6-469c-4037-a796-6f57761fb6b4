/**
 * 调试脚本 - 检查预测记录和验证任务
 */

const { PrismaClient } = require("@prisma/client");

const prisma = new PrismaClient({
  log: ["query", "error", "warn"],
});

async function debugPredictions() {
  try {
    console.log("🔍 开始检查数据库中的预测数据...\n");

    // 1. 检查预测记录
    console.log("=== 预测记录 (PredictionRecord) ===");
    const predictionRecords = await prisma.predictionRecord.findMany({
      orderBy: { timestamp: "desc" },
      take: 10,
    });

    console.log(`总预测记录数: ${predictionRecords.length}`);
    if (predictionRecords.length > 0) {
      console.log("最近的预测记录:");
      predictionRecords.forEach((record, index) => {
        console.log(`${index + 1}. ID: ${record.id}`);
        console.log(`   币种: ${record.symbol}`);
        console.log(`   方向: ${record.direction}`);
        console.log(`   时间框架: ${record.timeframe}`);
        console.log(`   创建时间: ${record.timestamp}`);
        console.log(`   是否已验证: ${record.verifiedAt ? "是" : "否"}`);
        console.log("");
      });
    } else {
      console.log("❌ 没有找到任何预测记录");
    }

    // 2. 检查验证任务
    console.log("\n=== 验证任务 (ValidationTask) ===");
    const validationTasks = await prisma.validationTask.findMany({
      orderBy: { createdAt: "desc" },
      take: 10,
    });

    console.log(`总验证任务数: ${validationTasks.length}`);
    if (validationTasks.length > 0) {
      console.log("最近的验证任务:");
      validationTasks.forEach((task, index) => {
        console.log(`${index + 1}. ID: ${task.id}`);
        console.log(`   预测ID: ${task.predictionId}`);
        console.log(`   状态: ${task.status}`);
        console.log(`   计划时间: ${task.scheduledAt}`);
        console.log(`   执行时间: ${task.executedAt || "未执行"}`);
        console.log(`   创建时间: ${task.createdAt}`);
        console.log("");
      });
    } else {
      console.log("❌ 没有找到任何验证任务");
    }

    // 3. 检查待验证的任务
    console.log("\n=== 待验证的任务 ===");
    const now = new Date();
    console.log(`当前时间: ${now}`);

    // 先检查所有PENDING任务的时间
    const allPendingTasks = await prisma.validationTask.findMany({
      where: {
        status: "PENDING",
      },
      orderBy: { scheduledAt: "asc" },
    });

    console.log(`所有PENDING任务数: ${allPendingTasks.length}`);
    if (allPendingTasks.length > 0) {
      console.log("所有PENDING任务的时间比较:");
      allPendingTasks.forEach((task, index) => {
        const timeDiff = now.getTime() - task.scheduledAt.getTime();
        const minutesDiff = Math.floor(timeDiff / (1000 * 60));
        const shouldBeReady = task.scheduledAt <= now;

        console.log(`${index + 1}. ID: ${task.id}`);
        console.log(`   计划时间: ${task.scheduledAt}`);
        console.log(`   当前时间: ${now}`);
        console.log(`   时间差: ${minutesDiff}分钟`);
        console.log(`   应该执行: ${shouldBeReady ? "是" : "否"}`);
        console.log("");
      });
    }

    const pendingTasks = await prisma.validationTask.findMany({
      where: {
        status: "PENDING",
        scheduledAt: {
          lte: new Date(),
        },
      },
      orderBy: { scheduledAt: "asc" },
    });

    console.log(`符合条件的待验证任务数: ${pendingTasks.length}`);
    if (pendingTasks.length > 0) {
      console.log("待验证任务详情:");
      pendingTasks.forEach((task, index) => {
        const timeDiff = now.getTime() - task.scheduledAt.getTime();
        const minutesOverdue = Math.floor(timeDiff / (1000 * 60));

        console.log(`${index + 1}. ID: ${task.id}`);
        console.log(`   预测ID: ${task.predictionId}`);
        console.log(`   计划时间: ${task.scheduledAt}`);
        console.log(
          `   超时时间: ${
            minutesOverdue > 0 ? minutesOverdue + "分钟" : "未超时"
          }`
        );
        console.log("");
      });
    } else {
      console.log("❌ 没有符合条件的待验证任务");
    }

    // 4. 检查系统配置
    console.log("\n=== 系统配置 ===");
    const configs = await prisma.systemConfig.findMany();
    console.log("系统配置:");
    configs.forEach((config) => {
      console.log(`${config.key}: ${config.value}`);
    });

    // 5. 检查最近的分析结果
    console.log("\n=== 分析结果 ===");
    const analysisResults = await prisma.analysisResult.findMany({
      orderBy: { timestamp: "desc" },
      take: 5,
    });

    console.log(`总分析结果数: ${analysisResults.length}`);
    if (analysisResults.length > 0) {
      console.log("最近的分析结果:");
      analysisResults.forEach((result, index) => {
        console.log(`${index + 1}. ID: ${result.id}`);
        console.log(`   币种: ${result.symbol}`);
        console.log(`   创建时间: ${result.timestamp}`);
        console.log("");
      });
    }
  } catch (error) {
    console.error("❌ 调试过程中发生错误:", error);
  } finally {
    await prisma.$disconnect();
  }
}

// 运行调试
debugPredictions();
