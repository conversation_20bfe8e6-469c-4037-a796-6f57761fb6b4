"use client";

import React, { useState, useEffect } from "react";
import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Progress } from "@/components/ui/progress";
import {
  TrendingUp,
  TrendingDown,
  DollarSign,
  Target,
  Shield,
  AlertTriangle,
  Clock,
  Zap,
} from "lucide-react";
import {
  formatNumber,
  formatPrice,
  abs,
  multiply,
  divide,
  gte,
  gt,
  lt,
  toNumber,
} from "@/lib/big-utils";

interface Position {
  symbol: string;
  direction: "LONG" | "SHORT";
  entryPrice: number;
  currentPrice: number;
  quantity: number;
  unrealizedPnL: number;
  unrealizedPnLPercent: number;
  stopLoss: number;
  takeProfit: number[];
  timeHeld: number; // 持仓时间（分钟）
  confidence: number;
}

interface ProfitMetrics {
  totalPnL: number;
  totalPnLPercent: number;
  winRate: number;
  avgWin: number;
  avgLoss: number;
  maxDrawdown: number;
  sharpeRatio: number;
  profitFactor: number;
  totalTrades: number;
  winningTrades: number;
  losingTrades: number;
}

interface ProfitMonitorProps {
  positions: Position[];
  metrics: ProfitMetrics;
  onClosePosition: (symbol: string) => void;
  onAdjustStopLoss: (symbol: string, newStopLoss: number) => void;
  onPartialExit: (symbol: string, percentage: number) => void;
}

export function ProfitMonitor({
  positions,
  metrics,
  onClosePosition,
  onAdjustStopLoss,
  onPartialExit,
}: ProfitMonitorProps) {
  const [selectedPosition, setSelectedPosition] = useState<string | null>(null);
  const [autoManagement, setAutoManagement] = useState(true);

  // 实时更新逻辑
  useEffect(() => {
    const interval = setInterval(() => {
      if (autoManagement) {
        positions.forEach((position) => {
          checkProfitTargets(position);
          checkStopLoss(position);
          checkTrailingStop(position);
        });
      }
    }, 1000);

    return () => clearInterval(interval);
  }, [positions, autoManagement]);

  const checkProfitTargets = (position: Position) => {
    const profitPercent = abs(position.unrealizedPnLPercent);

    // 分批止盈逻辑
    if (gte(profitPercent, 8) && position.quantity > 0.5) {
      onPartialExit(position.symbol, 50); // 8%利润时止盈50%
    } else if (gte(profitPercent, 5) && position.quantity > 0.7) {
      onPartialExit(position.symbol, 30); // 5%利润时止盈30%
    } else if (gte(profitPercent, 3) && position.quantity > 0.8) {
      onPartialExit(position.symbol, 20); // 3%利润时止盈20%
    }
  };

  const checkStopLoss = (position: Position) => {
    const isLoss = position.unrealizedPnL < 0;
    const lossPercent = abs(position.unrealizedPnLPercent);

    // 动态止损
    if (isLoss && gte(lossPercent, 2.5)) {
      onClosePosition(position.symbol);
    }
  };

  const checkTrailingStop = (position: Position) => {
    const profitPercent = position.unrealizedPnLPercent;

    if (gt(profitPercent, 2)) {
      // 追踪止损：保护50%利润
      const trailingStopPrice =
        position.direction === "LONG"
          ? multiply(position.currentPrice, 0.99) // 1%追踪距离
          : multiply(position.currentPrice, 1.01);

      if (
        position.direction === "LONG" &&
        gt(trailingStopPrice, position.stopLoss)
      ) {
        onAdjustStopLoss(position.symbol, toNumber(trailingStopPrice));
      } else if (
        position.direction === "SHORT" &&
        lt(trailingStopPrice, position.stopLoss)
      ) {
        onAdjustStopLoss(position.symbol, toNumber(trailingStopPrice));
      }
    }
  };

  const getPositionStatus = (position: Position) => {
    const profitPercent = position.unrealizedPnLPercent;

    if (profitPercent >= 5)
      return { status: "excellent", color: "bg-green-500" };
    if (profitPercent >= 2) return { status: "good", color: "bg-green-400" };
    if (profitPercent >= 0)
      return { status: "positive", color: "bg-green-300" };
    if (profitPercent >= -1)
      return { status: "slight-loss", color: "bg-yellow-400" };
    if (profitPercent >= -2)
      return { status: "moderate-loss", color: "bg-orange-400" };
    return { status: "significant-loss", color: "bg-red-500" };
  };

  const formatTime = (minutes: number) => {
    if (minutes < 60) return `${minutes}分钟`;
    const hours = Math.floor(minutes / 60);
    const mins = minutes % 60;
    return `${hours}小时${mins}分钟`;
  };

  return (
    <div className="space-y-6">
      {/* 总体盈利概览 */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <DollarSign className="h-5 w-5" />
            盈利监控总览
            <Badge variant={autoManagement ? "default" : "secondary"}>
              {autoManagement ? "自动管理" : "手动管理"}
            </Badge>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="text-center">
              <div
                className={`text-2xl font-bold ${
                  metrics.totalPnL >= 0 ? "text-green-600" : "text-red-600"
                }`}
              >
                ${formatNumber(metrics.totalPnL)}
              </div>
              <div className="text-sm text-gray-500">总盈亏</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-blue-600">
                {formatNumber(metrics.winRate, 1)}%
              </div>
              <div className="text-sm text-gray-500">胜率</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-purple-600">
                {formatNumber(metrics.profitFactor)}
              </div>
              <div className="text-sm text-gray-500">盈亏比</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-orange-600">
                {formatNumber(metrics.sharpeRatio)}
              </div>
              <div className="text-sm text-gray-500">夏普比率</div>
            </div>
          </div>

          <div className="mt-4">
            <div className="flex justify-between text-sm mb-2">
              <span>最大回撤</span>
              <span className="text-red-600">
                {formatNumber(metrics.maxDrawdown)}%
              </span>
            </div>
            <Progress value={Math.abs(metrics.maxDrawdown)} className="h-2" />
          </div>
        </CardContent>
      </Card>

      {/* 持仓列表 */}
      <div className="grid gap-4">
        {positions.map((position) => {
          const { status, color } = getPositionStatus(position);
          const isSelected = selectedPosition === position.symbol;

          return (
            <Card
              key={position.symbol}
              className={`cursor-pointer transition-all ${
                isSelected ? "ring-2 ring-blue-500" : ""
              }`}
              onClick={() =>
                setSelectedPosition(isSelected ? null : position.symbol)
              }
            >
              <CardHeader className="pb-3">
                <div className="flex items-center justify-between">
                  <CardTitle className="flex items-center gap-2">
                    {position.direction === "LONG" ? (
                      <TrendingUp className="h-4 w-4 text-green-500" />
                    ) : (
                      <TrendingDown className="h-4 w-4 text-red-500" />
                    )}
                    {position.symbol}
                    <Badge variant="outline">{position.direction}</Badge>
                  </CardTitle>
                  <div className="flex items-center gap-2">
                    <div className={`w-3 h-3 rounded-full ${color}`} />
                    <span
                      className={`font-bold ${
                        position.unrealizedPnL >= 0
                          ? "text-green-600"
                          : "text-red-600"
                      }`}
                    >
                      {position.unrealizedPnLPercent >= 0 ? "+" : ""}
                      {formatNumber(position.unrealizedPnLPercent)}%
                    </span>
                  </div>
                </div>
              </CardHeader>

              <CardContent>
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                  <div>
                    <div className="text-gray-500">入场价格</div>
                    <div className="font-medium">
                      ${formatPrice(position.entryPrice)}
                    </div>
                  </div>
                  <div>
                    <div className="text-gray-500">当前价格</div>
                    <div className="font-medium">
                      ${formatPrice(position.currentPrice)}
                    </div>
                  </div>
                  <div>
                    <div className="text-gray-500">持仓时间</div>
                    <div className="font-medium flex items-center gap-1">
                      <Clock className="h-3 w-3" />
                      {formatTime(position.timeHeld)}
                    </div>
                  </div>
                  <div>
                    <div className="text-gray-500">信心度</div>
                    <div className="font-medium">{position.confidence}%</div>
                  </div>
                </div>

                {isSelected && (
                  <div className="mt-4 pt-4 border-t space-y-3">
                    <div className="grid grid-cols-2 gap-4 text-sm">
                      <div>
                        <div className="text-gray-500">止损价格</div>
                        <div className="font-medium text-red-600">
                          ${formatPrice(position.stopLoss)}
                        </div>
                      </div>
                      <div>
                        <div className="text-gray-500">止盈目标</div>
                        <div className="font-medium text-green-600">
                          $
                          {position.takeProfit
                            .map((tp) => formatPrice(tp))
                            .join(", ")}
                        </div>
                      </div>
                    </div>

                    <div className="flex gap-2 flex-wrap">
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={(e) => {
                          e.stopPropagation();
                          onPartialExit(position.symbol, 25);
                        }}
                      >
                        <Target className="h-3 w-3 mr-1" />
                        止盈25%
                      </Button>
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={(e) => {
                          e.stopPropagation();
                          onPartialExit(position.symbol, 50);
                        }}
                      >
                        <Target className="h-3 w-3 mr-1" />
                        止盈50%
                      </Button>
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={(e) => {
                          e.stopPropagation();
                          onClosePosition(position.symbol);
                        }}
                      >
                        <Shield className="h-3 w-3 mr-1" />
                        平仓
                      </Button>
                    </div>

                    {position.unrealizedPnLPercent < -1.5 && (
                      <div className="flex items-center gap-2 p-2 bg-red-50 rounded-lg">
                        <AlertTriangle className="h-4 w-4 text-red-500" />
                        <span className="text-sm text-red-700">
                          建议考虑止损，当前亏损已达到
                          {formatNumber(abs(position.unrealizedPnLPercent), 1)}%
                        </span>
                      </div>
                    )}

                    {position.unrealizedPnLPercent > 3 && (
                      <div className="flex items-center gap-2 p-2 bg-green-50 rounded-lg">
                        <Zap className="h-4 w-4 text-green-500" />
                        <span className="text-sm text-green-700">
                          盈利良好，建议考虑分批止盈锁定利润
                        </span>
                      </div>
                    )}
                  </div>
                )}
              </CardContent>
            </Card>
          );
        })}
      </div>

      {positions.length === 0 && (
        <Card>
          <CardContent className="text-center py-8">
            <div className="text-gray-500">暂无持仓</div>
          </CardContent>
        </Card>
      )}

      {/* 自动管理控制 */}
      <Card>
        <CardHeader>
          <CardTitle>自动盈利管理</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-between">
            <div>
              <div className="font-medium">智能止盈止损</div>
              <div className="text-sm text-gray-500">
                自动执行分批止盈、追踪止损和风险控制
              </div>
            </div>
            <Button
              variant={autoManagement ? "default" : "outline"}
              onClick={() => setAutoManagement(!autoManagement)}
            >
              {autoManagement ? "已启用" : "已禁用"}
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
