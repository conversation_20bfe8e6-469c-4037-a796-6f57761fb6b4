-- CreateTable
CREATE TABLE "prediction_records" (
    "id" TEXT NOT NULL PRIMARY KEY,
    "symbol" TEXT NOT NULL,
    "timestamp" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "direction" TEXT NOT NULL,
    "entryPrice" REAL NOT NULL,
    "targetPrice" REAL NOT NULL,
    "stopLoss" REAL NOT NULL,
    "timeframe" TEXT NOT NULL,
    "confidence" INTEGER NOT NULL,
    "reasoning" TEXT NOT NULL,
    "actualPrice" REAL,
    "actualDirection" TEXT,
    "priceChange" REAL,
    "priceChangePercent" REAL,
    "hitTarget" BOOLEAN,
    "hitStopLoss" BOOLEAN,
    "verifiedAt" DATETIME,
    "directionCorrect" BOOLEAN,
    "priceAccuracy" REAL,
    "overallScore" REAL,
    "analysisResultId" TEXT,
    CONSTRAINT "prediction_records_analysisResultId_fkey" FOREIGN KEY ("analysisResultId") REFERENCES "analysis_results" ("id") ON DELETE SET NULL ON UPDATE CASCADE
);

-- CreateTable
CREATE TABLE "analysis_results" (
    "id" TEXT NOT NULL PRIMARY KEY,
    "symbol" TEXT NOT NULL,
    "timestamp" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "shortTermTrend" TEXT NOT NULL,
    "mediumTermTrend" TEXT NOT NULL,
    "longTermTrend" TEXT NOT NULL,
    "signalDirection" TEXT NOT NULL,
    "signalConfidence" INTEGER NOT NULL,
    "signalEntryPrice" REAL NOT NULL,
    "signalStopLoss" REAL NOT NULL,
    "signalTakeProfit" TEXT NOT NULL,
    "signalReasoning" TEXT NOT NULL,
    "maxDrawdown" REAL NOT NULL,
    "riskReward" REAL NOT NULL,
    "winRate" REAL NOT NULL,
    "expectedReturn" REAL NOT NULL,
    "volatility" TEXT NOT NULL,
    "volume" TEXT NOT NULL,
    "momentum" TEXT NOT NULL,
    "technicalIndicators" TEXT NOT NULL,
    "aiInsights" TEXT NOT NULL,
    "warnings" TEXT NOT NULL,
    "dataQualityScore" REAL,
    "dataQualityIssues" TEXT
);

-- CreateTable
CREATE TABLE "accuracy_stats" (
    "id" TEXT NOT NULL PRIMARY KEY,
    "symbol" TEXT,
    "timeframe" TEXT,
    "date" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "totalPredictions" INTEGER NOT NULL,
    "verifiedPredictions" INTEGER NOT NULL,
    "directionAccuracy" REAL NOT NULL,
    "priceAccuracy" REAL NOT NULL,
    "overallAccuracy" REAL NOT NULL,
    "highConfidenceCount" INTEGER NOT NULL DEFAULT 0,
    "highConfidenceAccuracy" REAL NOT NULL DEFAULT 0,
    "mediumConfidenceCount" INTEGER NOT NULL DEFAULT 0,
    "mediumConfidenceAccuracy" REAL NOT NULL DEFAULT 0,
    "lowConfidenceCount" INTEGER NOT NULL DEFAULT 0,
    "lowConfidenceAccuracy" REAL NOT NULL DEFAULT 0,
    "recentTrend" REAL NOT NULL DEFAULT 0
);

-- CreateTable
CREATE TABLE "data_quality_records" (
    "id" TEXT NOT NULL PRIMARY KEY,
    "symbol" TEXT NOT NULL,
    "timestamp" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "isValid" BOOLEAN NOT NULL,
    "score" REAL NOT NULL,
    "issues" TEXT NOT NULL,
    "warnings" TEXT NOT NULL,
    "recommendations" TEXT NOT NULL,
    "dataCompleteness" BOOLEAN NOT NULL DEFAULT true,
    "dataConsistency" BOOLEAN NOT NULL DEFAULT true,
    "dataFreshness" BOOLEAN NOT NULL DEFAULT true,
    "hasOutliers" BOOLEAN NOT NULL DEFAULT false,
    "volumeAnomalies" BOOLEAN NOT NULL DEFAULT false
);

-- CreateTable
CREATE TABLE "system_configs" (
    "id" TEXT NOT NULL PRIMARY KEY,
    "key" TEXT NOT NULL,
    "value" TEXT NOT NULL,
    "createdAt" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" DATETIME NOT NULL
);

-- CreateTable
CREATE TABLE "validation_tasks" (
    "id" TEXT NOT NULL PRIMARY KEY,
    "predictionId" TEXT NOT NULL,
    "scheduledAt" DATETIME NOT NULL,
    "executedAt" DATETIME,
    "status" TEXT NOT NULL DEFAULT 'PENDING',
    "errorMessage" TEXT,
    "createdAt" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" DATETIME NOT NULL
);

-- CreateIndex
CREATE UNIQUE INDEX "accuracy_stats_symbol_timeframe_date_key" ON "accuracy_stats"("symbol", "timeframe", "date");

-- CreateIndex
CREATE UNIQUE INDEX "system_configs_key_key" ON "system_configs"("key");
