import { OpenAIConfig, ConfigTestResult } from "@/types/trading";

export class ConfigService {
  private static instance: ConfigService;
  private readonly STORAGE_KEY = "ai-trading-config";

  // 默认配置
  private readonly DEFAULT_CONFIG: OpenAIConfig = {
    apiKey: "",
    baseURL: "https://api.openai.com/v1",
    model: "claude-sonnet-4-20250514",
  };

  static getInstance(): ConfigService {
    if (!ConfigService.instance) {
      ConfigService.instance = new ConfigService();
    }
    return ConfigService.instance;
  }

  /**
   * 获取当前配置
   * 优先级：本地存储 > 环境变量 > 默认配置
   */
  getConfig(): OpenAIConfig {
    // 尝试从本地存储获取
    if (typeof window !== "undefined") {
      try {
        const stored = localStorage.getItem(this.STORAGE_KEY);
        if (stored) {
          const config = JSON.parse(stored);
          // 验证配置完整性
          if (this.isValidConfig(config)) {
            return config;
          }
        }
      } catch (error) {
        console.warn("读取本地配置失败:", error);
      }
    }

    // 从环境变量获取（仅在服务端）
    if (typeof window === "undefined") {
      return {
        apiKey: process.env.OPENAI_API_KEY || this.DEFAULT_CONFIG.apiKey,
        baseURL: process.env.OPENAI_BASE_URL || this.DEFAULT_CONFIG.baseURL,
        model: process.env.OPENAI_MODEL || this.DEFAULT_CONFIG.model,
      };
    }

    return this.DEFAULT_CONFIG;
  }

  /**
   * 保存配置到本地存储
   */
  saveConfig(config: OpenAIConfig): void {
    if (typeof window === "undefined") {
      throw new Error("配置只能在客户端保存");
    }

    if (!this.isValidConfig(config)) {
      throw new Error("配置格式无效");
    }

    try {
      localStorage.setItem(this.STORAGE_KEY, JSON.stringify(config));
    } catch (error) {
      throw new Error("保存配置失败: " + error);
    }
  }

  /**
   * 清除本地配置
   */
  clearConfig(): void {
    if (typeof window !== "undefined") {
      localStorage.removeItem(this.STORAGE_KEY);
    }
  }

  /**
   * 验证配置是否有效
   */
  private isValidConfig(config: any): config is OpenAIConfig {
    return (
      config &&
      typeof config.apiKey === "string" &&
      typeof config.baseURL === "string" &&
      typeof config.model === "string" &&
      config.baseURL.startsWith("http")
    );
  }

  /**
   * 测试配置是否可用
   */
  async testConfig(config: OpenAIConfig): Promise<ConfigTestResult> {
    if (!this.isValidConfig(config)) {
      return {
        success: false,
        message: "配置格式无效",
      };
    }

    if (!config.apiKey.trim()) {
      return {
        success: false,
        message: "API Key 不能为空",
      };
    }

    try {
      const startTime = Date.now();

      const response = await fetch("/api/config/test", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(config),
      });

      const result = await response.json();
      const latency = Date.now() - startTime;

      if (result.success) {
        return {
          success: true,
          message: "配置测试成功",
          latency,
        };
      } else {
        return {
          success: false,
          message: result.error || "配置测试失败",
        };
      }
    } catch (error) {
      return {
        success: false,
        message: "网络错误: " + error,
      };
    }
  }

  /**
   * 获取可用的模型列表
   */
  getAvailableModels(): string[] {
    return ["claude-sonnet-4-20250514", "grok-3", "deepseek-r1-250528"];
  }

  /**
   * 获取常用的 Base URL 列表
   */
  getCommonBaseURLs(): string[] {
    return [
      "https://api.openai.com/v1",
      "https://api.anthropic.com/v1",
      "https://api.deepseek.com/v1",
      "https://api.moonshot.cn/v1",
      "https://api.zhipuai.cn/api/paas/v4",
      "https://yunwu.ai/v1",
      "http://airouter.mxyhi.com/v1",
    ];
  }
}

export const configService = ConfigService.getInstance();
