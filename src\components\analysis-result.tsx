"use client";

import React from "react";
import {
  TrendingUp,
  TrendingDown,
  AlertTriangle,
  Target,
  Shield,
  BarChart3,
  Clock,
  DollarSign,
} from "lucide-react";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { Separator } from "@/components/ui/separator";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { MarketMakerAnalysisComponent } from "./market-maker-analysis";
import {
  formatNumber,
  formatPrice,
  formatPercentage,
  divide,
  gte,
  calculateRiskRewardRatio,
} from "@/lib/big-utils";

interface AnalysisResultProps {
  data: {
    analysis: any;
    advice: any;
    positionManagement: any;
    rollingStrategy: any;
    riskReward: number;
    trendAnalysis: any;
    marketData: any;
  };
  className?: string;
}

export function AnalysisResult({ data, className }: AnalysisResultProps) {
  const {
    analysis,
    advice,
    positionManagement,
    rollingStrategy,
    riskReward,
    trendAnalysis,
    marketData,
  } = data;

  const getTrendColor = (trend: string) => {
    switch (trend) {
      case "BULLISH":
        return "text-green-500";
      case "BEARISH":
        return "text-red-500";
      default:
        return "text-gray-500";
    }
  };

  const getTrendIcon = (trend: string) => {
    switch (trend) {
      case "BULLISH":
        return <TrendingUp className="h-4 w-4" />;
      case "BEARISH":
        return <TrendingDown className="h-4 w-4" />;
      default:
        return <BarChart3 className="h-4 w-4" />;
    }
  };

  const getActionColor = (action: string) => {
    switch (action) {
      case "BUY":
        return "bg-green-500";
      case "SELL":
        return "bg-red-500";
      default:
        return "bg-gray-500";
    }
  };

  const getRiskColor = (level: string) => {
    switch (level) {
      case "LOW":
        return "text-green-500";
      case "MEDIUM":
        return "text-yellow-500";
      case "HIGH":
        return "text-red-500";
      default:
        return "text-gray-500";
    }
  };

  return (
    <div className={`space-y-6 ${className}`}>
      {/* 市场概览 */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <BarChart3 className="h-5 w-5" />
            <span>市场概览 - {marketData.symbol}</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="text-center">
              <p className="text-sm text-gray-500">当前价格</p>
              <p className="text-lg font-semibold">
                ${formatPrice(marketData.latestPrice || 0)}
              </p>
            </div>
            <div className="text-center">
              <p className="text-sm text-gray-500">24h变化</p>
              <p
                className={`text-lg font-semibold ${
                  (marketData.dailyChange || 0) >= 0
                    ? "text-green-500"
                    : "text-red-500"
                }`}
              >
                {(marketData.dailyChange || 0) >= 0 ? "+" : ""}
                {formatPercentage(marketData.dailyChange || 0)}%
              </p>
            </div>
            <div className="text-center">
              <p className="text-sm text-gray-500">24h成交量</p>
              <p className="text-lg font-semibold">
                {formatNumber(divide(marketData.volume24h || 0, 1000000), 1)}M
              </p>
            </div>
            <div className="text-center">
              <p className="text-sm text-gray-500">分析时间</p>
              <p className="text-sm">
                {new Date(analysis.timestamp).toLocaleTimeString()}
              </p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* 趋势分析 */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <TrendingUp className="h-5 w-5" />
            <span>趋势分析</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="text-center">
              <p className="text-sm text-gray-500 mb-2">极短期趋势 (15m-2h)</p>
              <div
                className={`flex items-center justify-center space-x-2 ${getTrendColor(
                  analysis.marketTrend.shortTerm
                )}`}
              >
                {getTrendIcon(analysis.marketTrend.shortTerm)}
                <span className="font-medium">
                  {analysis.marketTrend.shortTerm}
                </span>
              </div>
            </div>
            <div className="text-center">
              <p className="text-sm text-gray-500 mb-2">短期趋势 (2-8h)</p>
              <div
                className={`flex items-center justify-center space-x-2 ${getTrendColor(
                  analysis.marketTrend.mediumTerm
                )}`}
              >
                {getTrendIcon(analysis.marketTrend.mediumTerm)}
                <span className="font-medium">
                  {analysis.marketTrend.mediumTerm}
                </span>
              </div>
            </div>
            <div className="text-center">
              <p className="text-sm text-gray-500 mb-2">中期趋势 (8-24h)</p>
              <div
                className={`flex items-center justify-center space-x-2 ${getTrendColor(
                  analysis.marketTrend.longTerm
                )}`}
              >
                {getTrendIcon(analysis.marketTrend.longTerm)}
                <span className="font-medium">
                  {analysis.marketTrend.longTerm}
                </span>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* 交易建议 */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Target className="h-5 w-5" />
            <span>交易建议</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-3">
                <Badge className={getActionColor(advice.action)}>
                  {advice.action === "BUY"
                    ? "买入"
                    : advice.action === "SELL"
                    ? "卖出"
                    : "观望"}
                </Badge>
                <span className="text-sm text-gray-500">信心度</span>
                <Progress value={advice.confidence} className="w-20" />
                <span className="text-sm font-medium">
                  {advice.confidence}%
                </span>
              </div>
              <Badge
                variant="outline"
                className={getRiskColor(advice.riskLevel)}
              >
                {advice.riskLevel} 风险
              </Badge>
            </div>

            <Separator />

            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <div>
                <p className="text-sm text-gray-500">入场价格</p>
                <p className="font-semibold">
                  ${formatPrice(advice.entryPrice || 0)}
                </p>
              </div>
              <div>
                <p className="text-sm text-gray-500">止损价格</p>
                <p className="font-semibold text-red-500">
                  ${formatPrice(advice.stopLoss || 0)}
                </p>
              </div>
              <div>
                <p className="text-sm text-gray-500">止盈目标</p>
                <p className="font-semibold text-green-500">
                  ${formatPrice(advice.takeProfit?.[0] || 0)}
                </p>
              </div>
              <div>
                <p className="text-sm text-gray-500">风险收益比</p>
                <p className="font-semibold">
                  1:{formatNumber(riskReward || 0, 1)}
                </p>
              </div>
            </div>

            <div className="bg-gray-50 p-3 rounded-lg">
              <p className="text-sm text-gray-700">{advice.reasoning}</p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* 滚仓策略 */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <DollarSign className="h-5 w-5" />
            <span>滚仓策略</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="text-center">
              <p className="text-sm text-gray-500">基础仓位</p>
              <p className="text-lg font-semibold">
                {rollingStrategy.basePosition}%
              </p>
            </div>
            <div className="text-center">
              <p className="text-sm text-gray-500">最大仓位</p>
              <p className="text-lg font-semibold">
                {rollingStrategy.maxPosition}%
              </p>
            </div>
            <div className="text-center">
              <p className="text-sm text-gray-500">加仓阈值</p>
              <p className="text-lg font-semibold">
                {rollingStrategy.additionThreshold}%
              </p>
            </div>
            <div className="text-center">
              <p className="text-sm text-gray-500">趋势确认</p>
              <Badge
                variant={
                  rollingStrategy.trendConfirmation ? "default" : "secondary"
                }
              >
                {rollingStrategy.trendConfirmation ? "已确认" : "待确认"}
              </Badge>
            </div>
          </div>

          <Separator className="my-4" />

          <div className="space-y-3">
            <h4 className="font-medium">加仓计划</h4>
            {positionManagement.addPositions
              ?.slice(0, 3)
              .map((position: any, index: number) => (
                <div
                  key={index}
                  className="flex items-center justify-between text-sm"
                >
                  <span>加仓 {index + 1}</span>
                  <span>${formatPrice(position.price || 0)}</span>
                  <span>{position.size}%</span>
                  <span className="text-gray-500 text-xs">
                    {position.condition}
                  </span>
                </div>
              ))}
          </div>
        </CardContent>
      </Card>

      {/* 技术指标 */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <BarChart3 className="h-5 w-5" />
            <span>技术指标</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="text-center">
              <p className="text-sm text-gray-500">RSI(14)</p>
              <p className="text-lg font-semibold">
                {formatNumber(analysis.technicalIndicators.rsi || 0, 1)}
              </p>
              <Progress
                value={analysis.technicalIndicators.rsi || 0}
                className="w-full mt-1"
              />
            </div>
            <div className="text-center">
              <p className="text-sm text-gray-500">MACD</p>
              <p
                className={`text-lg font-semibold ${
                  (analysis.technicalIndicators.macd?.macd || 0) >= 0
                    ? "text-green-500"
                    : "text-red-500"
                }`}
              >
                {formatNumber(analysis.technicalIndicators.macd?.macd || 0, 4)}
              </p>
            </div>
            <div className="text-center">
              <p className="text-sm text-gray-500">支撑位</p>
              <p className="text-lg font-semibold text-green-500">
                ${formatPrice(analysis.technicalIndicators.support || 0)}
              </p>
            </div>
            <div className="text-center">
              <p className="text-sm text-gray-500">阻力位</p>
              <p className="text-lg font-semibold text-red-500">
                ${formatPrice(analysis.technicalIndicators.resistance || 0)}
              </p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* 庄家行为与持仓分析 */}
      {(analysis.marketMakerAnalysis || analysis.spotFuturesAnalysis) && (
        <MarketMakerAnalysisComponent
          marketMakerAnalysis={analysis.marketMakerAnalysis}
          spotFuturesAnalysis={analysis.spotFuturesAnalysis}
        />
      )}

      {/* AI洞察和警告 */}
      {(analysis.aiInsights || analysis.warnings?.length > 0) && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <AlertTriangle className="h-5 w-5" />
              <span>AI洞察与风险提示</span>
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {analysis.aiInsights && (
              <div className="bg-blue-50 p-3 rounded-lg">
                <p className="text-sm text-blue-800">{analysis.aiInsights}</p>
              </div>
            )}

            {analysis.warnings?.map((warning: string, index: number) => (
              <Alert key={index}>
                <AlertTriangle className="h-4 w-4" />
                <AlertDescription>{warning}</AlertDescription>
              </Alert>
            ))}
          </CardContent>
        </Card>
      )}
    </div>
  );
}
