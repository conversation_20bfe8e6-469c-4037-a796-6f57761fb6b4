"use client";

import React from "react";
import {
  Eye,
  Brain,
  Target,
  Users,
  TrendingUp,
  TrendingDown,
  AlertTriangle,
  Activity,
  PieChart,
  BarChart3,
} from "lucide-react";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { Separator } from "@/components/ui/separator";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { MarketMakerAnalysis, SpotFuturesAnalysis } from "@/types/trading";

interface MarketMakerAnalysisProps {
  marketMakerAnalysis: MarketMakerAnalysis;
  spotFuturesAnalysis: SpotFuturesAnalysis;
  className?: string;
}

export function MarketMakerAnalysisComponent({
  marketMakerAnalysis,
  spotFuturesAnalysis,
  className,
}: MarketMakerAnalysisProps) {
  const getPatternColor = (pattern: string) => {
    switch (pattern) {
      case "PUMP_DUMP":
        return "bg-red-500";
      case "ACCUMULATION":
        return "bg-green-500";
      case "DISTRIBUTION":
        return "bg-orange-500";
      case "SQUEEZE":
        return "bg-purple-500";
      default:
        return "bg-gray-500";
    }
  };

  const getSentimentColor = (sentiment: string) => {
    switch (sentiment) {
      case "EXTREME_GREED":
        return "text-red-600";
      case "GREED":
        return "text-red-400";
      case "NEUTRAL":
        return "text-gray-500";
      case "FEAR":
        return "text-yellow-500";
      case "EXTREME_FEAR":
        return "text-green-500";
      default:
        return "text-gray-500";
    }
  };

  const getFlowColor = (flow: string) => {
    switch (flow) {
      case "STRONG_INFLOW":
        return "text-green-600";
      case "WEAK_INFLOW":
        return "text-green-400";
      case "NEUTRAL":
        return "text-gray-500";
      case "WEAK_OUTFLOW":
        return "text-red-400";
      case "STRONG_OUTFLOW":
        return "text-red-600";
      default:
        return "text-gray-500";
    }
  };

  const getRiskColor = (risk: string) => {
    switch (risk) {
      case "LOW":
        return "text-green-500";
      case "MEDIUM":
        return "text-yellow-500";
      case "HIGH":
        return "text-red-500";
      default:
        return "text-gray-500";
    }
  };

  return (
    <div className={`space-y-6 ${className}`}>
      {/* 庄家操盘信号 */}
      {/* <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Eye className="h-5 w-5" />
            <span>庄家操盘信号</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="text-center">
              <p className="text-sm text-gray-500 mb-2">对敲交易</p>
              <Progress
                value={marketMakerAnalysis.manipulationSignals.washTrading}
                className="mb-1"
              />
              <p className="text-sm font-medium">
                {marketMakerAnalysis.manipulationSignals.washTrading}%
              </p>
            </div>
            <div className="text-center">
              <p className="text-sm text-gray-500 mb-2">价格托盘</p>
              <Progress
                value={marketMakerAnalysis.manipulationSignals.priceSupport}
                className="mb-1"
              />
              <p className="text-sm font-medium">
                {marketMakerAnalysis.manipulationSignals.priceSupport}%
              </p>
            </div>
            <div className="text-center">
              <p className="text-sm text-gray-500 mb-2">异常放量</p>
              <Progress
                value={marketMakerAnalysis.manipulationSignals.volumeSpike}
                className="mb-1"
              />
              <p className="text-sm font-medium">
                {marketMakerAnalysis.manipulationSignals.volumeSpike}%
              </p>
            </div>
            <div className="text-center">
              <p className="text-sm text-gray-500 mb-2">假突破</p>
              <Progress
                value={marketMakerAnalysis.manipulationSignals.falseBreakout}
                className="mb-1"
              />
              <p className="text-sm font-medium">
                {marketMakerAnalysis.manipulationSignals.falseBreakout}%
              </p>
            </div>
          </div>
        </CardContent>
      </Card> */}

      {/* 市场心理分析 */}
      {/* <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Brain className="h-5 w-5" />
            <span>市场心理分析</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-500">恐惧贪婪指数</span>
                  <span className="font-medium">
                    {marketMakerAnalysis.psychologyAnalysis.fearGreedIndex}
                  </span>
                </div>
                <Progress
                  value={marketMakerAnalysis.psychologyAnalysis.fearGreedIndex}
                  className="w-full"
                />

                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-500">市场情绪</span>
                  <Badge
                    className={getSentimentColor(
                      marketMakerAnalysis.psychologyAnalysis.marketSentiment
                    )}
                  >
                    {marketMakerAnalysis.psychologyAnalysis.marketSentiment}
                  </Badge>
                </div>
              </div>
            </div>

            <div>
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-500">散户行为</span>
                  <Badge variant="outline">
                    {marketMakerAnalysis.psychologyAnalysis.retailBehavior}
                  </Badge>
                </div>

                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-500">聪明钱流向</span>
                  <div className="flex items-center space-x-2">
                    {marketMakerAnalysis.psychologyAnalysis.smartMoneyFlow ===
                    "INFLOW" ? (
                      <TrendingUp className="h-4 w-4 text-green-500" />
                    ) : marketMakerAnalysis.psychologyAnalysis
                        .smartMoneyFlow === "OUTFLOW" ? (
                      <TrendingDown className="h-4 w-4 text-red-500" />
                    ) : (
                      <Activity className="h-4 w-4 text-gray-500" />
                    )}
                    <span className="text-sm font-medium">
                      {marketMakerAnalysis.psychologyAnalysis.smartMoneyFlow}
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card> */}

      {/* 庄家操盘模式 */}
      {/* <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Target className="h-5 w-5" />
            <span>庄家操盘模式</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-3">
                <Badge
                  className={getPatternColor(
                    marketMakerAnalysis.manipulationPatterns.pattern
                  )}
                >
                  {marketMakerAnalysis.manipulationPatterns.pattern}
                </Badge>
                <span className="text-sm text-gray-500">阶段</span>
                <Badge variant="outline">
                  {marketMakerAnalysis.manipulationPatterns.stage}
                </Badge>
              </div>
              <div className="flex items-center space-x-2">
                <span className="text-sm text-gray-500">信心度</span>
                <Progress
                  value={marketMakerAnalysis.manipulationPatterns.confidence}
                  className="w-20"
                />
                <span className="text-sm font-medium">
                  {marketMakerAnalysis.manipulationPatterns.confidence}%
                </span>
              </div>
            </div>

            <Separator />

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <p className="text-sm text-gray-500 mb-1">预计时间周期</p>
                <p className="font-medium">
                  {marketMakerAnalysis.manipulationPatterns.timeframe}
                </p>
              </div>
              <div>
                <p className="text-sm text-gray-500 mb-1">目标价位</p>
                <p className="font-medium">
                  $
                  {marketMakerAnalysis.intentions.targetPrice?.toFixed(4) ||
                    "N/A"}
                </p>
              </div>
            </div>

            <div className="bg-gray-50 p-3 rounded-lg">
              <p className="text-sm text-gray-700">
                {marketMakerAnalysis.manipulationPatterns.description}
              </p>
            </div>

            <div className="bg-blue-50 p-3 rounded-lg">
              <p className="text-sm font-medium text-blue-800 mb-1">
                庄家意图分析
              </p>
              <p className="text-sm text-blue-700">
                {marketMakerAnalysis.intentions.reasoning}
              </p>
            </div>
          </div>
        </CardContent>
      </Card> */}

      {/* 现货期货分析 */}
      {/* <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <BarChart3 className="h-5 w-5" />
            <span>现货期货分析</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <span className="text-sm text-gray-500">市场类型</span>
              <Badge
                className={
                  spotFuturesAnalysis.marketType === "FUTURES"
                    ? "bg-orange-500"
                    : "bg-blue-500"
                }
              >
                {spotFuturesAnalysis.marketType}
              </Badge>
            </div>

            {spotFuturesAnalysis.marketType === "FUTURES" && (
              <>
                <Separator />
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  {spotFuturesAnalysis.basisSpread !== undefined && (
                    <div className="text-center">
                      <p className="text-sm text-gray-500">基差</p>
                      <p className="font-medium">
                        {spotFuturesAnalysis.basisSpread?.toFixed(4) || "N/A"}
                      </p>
                    </div>
                  )}
                  {spotFuturesAnalysis.fundingRate !== undefined && (
                    <div className="text-center">
                      <p className="text-sm text-gray-500">资金费率</p>
                      <p
                        className={`font-medium ${
                          (spotFuturesAnalysis.fundingRate || 0) >= 0
                            ? "text-green-500"
                            : "text-red-500"
                        }`}
                      >
                        {((spotFuturesAnalysis.fundingRate || 0) * 100).toFixed(
                          4
                        )}
                        %
                      </p>
                    </div>
                  )}
                  {spotFuturesAnalysis.leverageImpact && (
                    <div className="text-center">
                      <p className="text-sm text-gray-500">爆仓风险</p>
                      <Badge
                        className={getRiskColor(
                          spotFuturesAnalysis.leverageImpact.liquidationRisk
                        )}
                      >
                        {spotFuturesAnalysis.leverageImpact.liquidationRisk}
                      </Badge>
                    </div>
                  )}
                </div>
              </>
            )}

            <Separator />

            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-500">推荐市场</span>
                <Badge variant="outline">
                  {spotFuturesAnalysis.recommendations.preferredMarket}
                </Badge>
              </div>

              <div className="bg-gray-50 p-3 rounded-lg">
                <p className="text-sm text-gray-700">
                  {spotFuturesAnalysis.recommendations.reasoning}
                </p>
              </div>

              {spotFuturesAnalysis.recommendations.riskAdjustments.length >
                0 && (
                <div className="space-y-2">
                  <p className="text-sm font-medium text-gray-700">
                    风险调整建议：
                  </p>
                  {spotFuturesAnalysis.recommendations.riskAdjustments.map(
                    (adjustment, index) => (
                      <Alert key={index}>
                        <AlertTriangle className="h-4 w-4" />
                        <AlertDescription className="text-sm">
                          {adjustment}
                        </AlertDescription>
                      </Alert>
                    )
                  )}
                </div>
              )}
            </div>
          </div>
        </CardContent>
      </Card> */}
    </div>
  );
}
