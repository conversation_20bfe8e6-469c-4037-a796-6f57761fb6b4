"use client";

import { useState, useEffect } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Switch } from "@/components/ui/switch";
import { Badge } from "@/components/ui/badge";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import {
  Loader2,
  CheckCircle,
  XCircle,
  Eye,
  EyeOff,
  Shield,
} from "lucide-react";
import { toast } from "sonner";
import { tradingConfigService } from "@/lib/trading-config-service";
import {
  TradingConfigFormData,
  TradingConfigTestResult,
} from "@/types/trading";

const tradingConfigSchema = z.object({
  name: z.string().min(1, "配置名称不能为空"),
  baseURL: z.string().url("请输入有效的 URL"),
  apiKey: z.string().min(1, "API Key 不能为空"),
  secretKey: z.string().min(1, "Secret Key 不能为空"),
  testnet: z.boolean(),
});

export function TradingConfigForm() {
  const [isLoading, setIsLoading] = useState(false);
  const [isTesting, setIsTesting] = useState(false);
  const [testResult, setTestResult] = useState<TradingConfigTestResult | null>(
    null
  );
  const [showApiKey, setShowApiKey] = useState(false);
  const [showSecretKey, setShowSecretKey] = useState(false);

  const form = useForm<TradingConfigFormData>({
    resolver: zodResolver(tradingConfigSchema),
    defaultValues: {
      name: "主网配置",
      baseURL: "https://fapi.binance.com",
      apiKey: "",
      secretKey: "",
      testnet: false,
    },
  });

  const watchedValues = form.watch();
  const commonExchanges = tradingConfigService.getCommonExchanges();

  // 加载现有配置
  useEffect(() => {
    const config = tradingConfigService.getConfig();
    form.setValue("name", config.name || "主网配置");
    form.setValue("baseURL", config.baseURL);
    form.setValue("apiKey", config.apiKey);
    form.setValue("secretKey", config.secretKey);
    form.setValue("testnet", config.testnet || false);
  }, [form]);

  const onSubmit = async (data: TradingConfigFormData) => {
    setIsLoading(true);

    const loadingToast = toast.loading("正在保存交易API配置...", {
      description: "正在验证并保存您的交易所配置信息",
    });

    try {
      const success = tradingConfigService.saveConfig(data);
      if (success) {
        toast.dismiss(loadingToast);
        toast.success("✅ 交易API配置保存成功！", {
          description: "您的交易所API配置已成功保存到本地存储",
          duration: 3000,
        });
        setTestResult(null); // 清除之前的测试结果
      } else {
        toast.dismiss(loadingToast);
        toast.error("❌ 保存配置失败", {
          description: "配置保存过程中出现错误，请重试",
          duration: 4000,
        });
      }
    } catch (error: any) {
      console.error("保存配置失败:", error);
      toast.dismiss(loadingToast);
      toast.error("❌ 保存失败", {
        description: error.message || "保存配置时发生未知错误",
        duration: 4000,
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleTest = async () => {
    const currentValues = form.getValues();
    setIsTesting(true);
    setTestResult(null);

    const loadingToast = toast.loading("正在测试交易API连接...", {
      description: "正在验证您的API密钥和网络连接",
    });

    try {
      const result = await tradingConfigService.testConfig(currentValues);
      setTestResult(result);

      toast.dismiss(loadingToast);

      if (result.success) {
        toast.success("🎉 交易API配置测试成功！", {
          description: "API连接正常，可以正常获取交易数据",
          duration: 4000,
        });
      } else {
        toast.error("❌ 交易API配置测试失败", {
          description: result.message,
          duration: 5000,
        });
      }
    } catch (error: any) {
      console.error("测试配置失败:", error);
      const errorMessage = "测试失败，请检查网络连接";
      setTestResult({
        success: false,
        message: errorMessage,
      });

      toast.dismiss(loadingToast);
      toast.error("❌ 测试失败", {
        description: error.message || errorMessage,
        duration: 5000,
      });
    } finally {
      setIsTesting(false);
    }
  };

  const handleClearConfig = () => {
    const loadingToast = toast.loading("正在清除配置...");

    try {
      tradingConfigService.clearConfig();
      form.reset({
        name: "主网配置",
        baseURL: "https://fapi.binance.com",
        apiKey: "",
        secretKey: "",
        testnet: false,
      });
      setTestResult(null);

      toast.dismiss(loadingToast);
      toast.success("🔄 配置已清除", {
        description: "所有交易API配置已恢复为默认值，本地存储已清空",
        duration: 3000,
      });
    } catch (error: any) {
      toast.dismiss(loadingToast);
      toast.error("❌ 清除失败", {
        description: error.message || "清除配置时发生错误",
        duration: 4000,
      });
    }
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Shield className="h-5 w-5" />
          交易API配置
        </CardTitle>
        <CardDescription>
          配置交易所API以启用实际交易功能。请确保API Key具有期货交易权限。
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
            {/* 配置名称 */}
            <FormField
              control={form.control}
              name="name"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>配置名称</FormLabel>
                  <FormControl>
                    <Input placeholder="输入配置名称" {...field} />
                  </FormControl>
                  <FormDescription>
                    为此配置设置一个便于识别的名称
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* 交易所选择 */}
            <div className="space-y-2">
              <FormLabel>选择交易所</FormLabel>
              <Select
                value={
                  commonExchanges.find(
                    (ex) => ex.baseURL === watchedValues.baseURL
                  )?.name || "custom"
                }
                onValueChange={(value) => {
                  const exchange = commonExchanges.find(
                    (ex) => ex.name === value
                  );
                  if (exchange) {
                    form.setValue("baseURL", exchange.baseURL);
                    form.setValue("testnet", exchange.testnet);
                    form.setValue("name", exchange.name);
                  }
                }}
              >
                <SelectTrigger>
                  <SelectValue placeholder="选择常用交易所" />
                </SelectTrigger>
                <SelectContent>
                  {commonExchanges.map((exchange) => (
                    <SelectItem key={exchange.name} value={exchange.name}>
                      <div className="flex items-center gap-2">
                        {exchange.name}
                        {exchange.testnet && (
                          <Badge variant="secondary" className="text-xs">
                            测试网
                          </Badge>
                        )}
                      </div>
                    </SelectItem>
                  ))}
                  <SelectItem value="custom">自定义配置</SelectItem>
                </SelectContent>
              </Select>
            </div>

            {/* Base URL */}
            <FormField
              control={form.control}
              name="baseURL"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Base URL</FormLabel>
                  <FormControl>
                    <Input placeholder="输入交易所API地址" {...field} />
                  </FormControl>
                  <FormDescription>
                    交易所API的基础地址，如 https://fapi.binance.com
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* 测试网开关 */}
            <FormField
              control={form.control}
              name="testnet"
              render={({ field }) => (
                <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                  <div className="space-y-0.5">
                    <FormLabel className="text-base">测试网模式</FormLabel>
                    <FormDescription>
                      启用后将使用测试网环境，不会产生实际交易
                    </FormDescription>
                  </div>
                  <FormControl>
                    <Switch
                      checked={field.value}
                      onCheckedChange={field.onChange}
                    />
                  </FormControl>
                </FormItem>
              )}
            />

            {/* API Key */}
            <FormField
              control={form.control}
              name="apiKey"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>API Key</FormLabel>
                  <FormControl>
                    <div className="relative">
                      <Input
                        type={showApiKey ? "text" : "password"}
                        placeholder="输入API Key"
                        {...field}
                        className="pr-10"
                      />
                      <Button
                        type="button"
                        variant="ghost"
                        size="sm"
                        className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                        onClick={() => setShowApiKey(!showApiKey)}
                      >
                        {showApiKey ? (
                          <EyeOff className="h-4 w-4" />
                        ) : (
                          <Eye className="h-4 w-4" />
                        )}
                      </Button>
                    </div>
                  </FormControl>
                  <FormDescription>
                    您的交易所API Key，请确保具有期货交易权限
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Secret Key */}
            <FormField
              control={form.control}
              name="secretKey"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Secret Key</FormLabel>
                  <FormControl>
                    <div className="relative">
                      <Input
                        type={showSecretKey ? "text" : "password"}
                        placeholder="输入Secret Key"
                        {...field}
                        className="pr-10"
                      />
                      <Button
                        type="button"
                        variant="ghost"
                        size="sm"
                        className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                        onClick={() => setShowSecretKey(!showSecretKey)}
                      >
                        {showSecretKey ? (
                          <EyeOff className="h-4 w-4" />
                        ) : (
                          <Eye className="h-4 w-4" />
                        )}
                      </Button>
                    </div>
                  </FormControl>
                  <FormDescription>
                    您的交易所Secret Key，用于API签名验证
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* 测试结果 */}
            {testResult && (
              <div
                className={`p-4 rounded-lg border ${
                  testResult.success
                    ? "bg-green-50 border-green-200"
                    : "bg-red-50 border-red-200"
                }`}
              >
                <div className="flex items-center gap-2 mb-2">
                  {testResult.success ? (
                    <CheckCircle className="h-4 w-4 text-green-600" />
                  ) : (
                    <XCircle className="h-4 w-4 text-red-600" />
                  )}
                  <span
                    className={`font-medium ${
                      testResult.success ? "text-green-800" : "text-red-800"
                    }`}
                  >
                    {testResult.message}
                  </span>
                  {testResult.latency && (
                    <Badge variant="outline" className="ml-auto">
                      {testResult.latency}ms
                    </Badge>
                  )}
                </div>
                {testResult.success && testResult.accountInfo && (
                  <div className="text-sm text-green-700 space-y-1">
                    <div>
                      总余额: {testResult.accountInfo.totalWalletBalance}
                    </div>
                    <div>
                      可用余额: {testResult.accountInfo.availableBalance}
                    </div>
                  </div>
                )}
              </div>
            )}

            {/* 操作按钮 */}
            <div className="flex gap-3">
              <Button type="submit" disabled={isLoading}>
                {isLoading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                保存配置
              </Button>

              <Button
                type="button"
                variant="outline"
                onClick={handleTest}
                disabled={
                  isTesting || !watchedValues.apiKey || !watchedValues.secretKey
                }
              >
                {isTesting && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                测试连接
              </Button>

              <AlertDialog>
                <AlertDialogTrigger asChild>
                  <Button type="button" variant="destructive">
                    清除配置
                  </Button>
                </AlertDialogTrigger>
                <AlertDialogContent>
                  <AlertDialogHeader>
                    <AlertDialogTitle>确认清除配置</AlertDialogTitle>
                    <AlertDialogDescription>
                      此操作将清除所有交易API配置信息，包括API Key和Secret Key。
                      此操作不可撤销。
                    </AlertDialogDescription>
                  </AlertDialogHeader>
                  <AlertDialogFooter>
                    <AlertDialogCancel>取消</AlertDialogCancel>
                    <AlertDialogAction onClick={handleClearConfig}>
                      确认清除
                    </AlertDialogAction>
                  </AlertDialogFooter>
                </AlertDialogContent>
              </AlertDialog>
            </div>
          </form>
        </Form>
      </CardContent>
    </Card>
  );
}
