/**
 * Prisma数据库客户端配置
 */

import { PrismaClient } from '@prisma/client'

const globalForPrisma = globalThis as unknown as {
  prisma: PrismaClient | undefined
}

export const prisma = globalForPrisma.prisma ?? new PrismaClient({
  log: process.env.NODE_ENV === 'development' ? ['query', 'error', 'warn'] : ['error'],
})

if (process.env.NODE_ENV !== 'production') globalForPrisma.prisma = prisma

// 数据库连接测试
export async function testDatabaseConnection(): Promise<boolean> {
  try {
    await prisma.$connect()
    console.log('✅ 数据库连接成功')
    return true
  } catch (error) {
    console.error('❌ 数据库连接失败:', error)
    return false
  }
}

// 优雅关闭数据库连接
export async function closeDatabaseConnection(): Promise<void> {
  try {
    await prisma.$disconnect()
    console.log('✅ 数据库连接已关闭')
  } catch (error) {
    console.error('❌ 关闭数据库连接失败:', error)
  }
}

// 数据库初始化
export async function initializeDatabase(): Promise<void> {
  try {
    // 测试连接
    await testDatabaseConnection()
    
    // 创建默认系统配置
    await createDefaultConfigs()
    
    console.log('✅ 数据库初始化完成')
  } catch (error) {
    console.error('❌ 数据库初始化失败:', error)
    throw error
  }
}

// 创建默认系统配置
async function createDefaultConfigs(): Promise<void> {
  const defaultConfigs = [
    {
      key: 'prediction_validation_enabled',
      value: JSON.stringify(true)
    },
    {
      key: 'data_quality_check_enabled', 
      value: JSON.stringify(true)
    },
    {
      key: 'min_data_quality_score',
      value: JSON.stringify(60)
    },
    {
      key: 'prediction_validation_interval',
      value: JSON.stringify(600000) // 10分钟
    }
  ]

  for (const config of defaultConfigs) {
    await prisma.systemConfig.upsert({
      where: { key: config.key },
      update: {},
      create: config
    })
  }
}

export default prisma
