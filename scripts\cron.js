#!/usr/bin/env node

/**
 * 独立的定时分析 Cron 脚本
 * 每10分钟执行一次BTC和ETH的激进型分析
 */

const axios = require("axios");
const cron = require("node-cron");

// 配置
const CONFIG = {
  // API 基础URL，可以通过环境变量配置
  API_BASE_URL: process.env.API_BASE_URL || "http://localhost:3000",
  // 定时任务表达式：每10分钟执行一次
  CRON_EXPRESSION: "*/10 * * * *",
  // 请求超时时间（毫秒）
  REQUEST_TIMEOUT: 300000, // 5分钟
  // 时区
  TIMEZONE: "Asia/Shanghai",
};

/**
 * 执行定时分析
 */
async function executeAnalysis() {
  const timestamp = new Date().toISOString();
  console.log(`[${timestamp}] 开始执行定时分析...`);

  try {
    // 1. 执行预测验证
    await validatePredictions();

    // 2. 执行新的分析
    const response = await axios.post(
      `${CONFIG.API_BASE_URL}/api/execute-analysis`,
      {},
      {
        timeout: CONFIG.REQUEST_TIMEOUT,
        headers: {
          "Content-Type": "application/json",
          "User-Agent": "AI-B-Cron-Script/1.0",
        },
      }
    );

    if (response.data.success) {
      console.log(`[${timestamp}] ✅ 定时分析执行成功:`, response.data.message);

      // 打印详细结果
      if (response.data.results) {
        response.data.results.forEach((result) => {
          if (result.success) {
            console.log(`  ✅ ${result.symbol}: 分析完成`);
          } else {
            console.log(`  ❌ ${result.symbol}: ${result.error}`);
          }
        });
      }
    } else {
      console.error(`[${timestamp}] ❌ 定时分析执行失败:`, response.data.error);
    }
  } catch (error) {
    console.error(`[${timestamp}] ❌ 请求失败:`, error.message);

    if (error.response) {
      console.error(`  状态码: ${error.response.status}`);
      console.error(`  响应数据:`, error.response.data);
    } else if (error.request) {
      console.error(`  网络错误: 无法连接到 ${CONFIG.API_BASE_URL}`);
    }
  }
}

/**
 * 验证历史预测
 */
async function validatePredictions() {
  const timestamp = new Date().toISOString();
  console.log(`[${timestamp}] 🔍 开始验证历史预测...`);

  try {
    const response = await axios.post(
      `${CONFIG.API_BASE_URL}/api/predictions/validate`,
      { batchValidate: true },
      {
        timeout: 60000, // 1分钟超时
        headers: {
          "Content-Type": "application/json",
          "User-Agent": "AI-B-Cron-Script/1.0",
        },
      }
    );

    if (response.data.success) {
      console.log(`[${timestamp}] ✅ 预测验证完成:`, response.data.message);
    } else {
      console.error(`[${timestamp}] ❌ 预测验证失败:`, response.data.error);
    }
  } catch (error) {
    console.error(`[${timestamp}] ❌ 预测验证请求失败:`, error.message);
  }
}

/**
 * 检查API服务状态
 */
async function checkApiStatus() {
  try {
    const response = await axios.get(
      `${CONFIG.API_BASE_URL}/api/execute-analysis`,
      {
        timeout: 10000, // 10秒超时
      }
    );

    if (response.data.success) {
      console.log("✅ API服务状态正常");
      console.log("  配置信息:", response.data.data);
      return true;
    } else {
      console.error("❌ API服务配置异常:", response.data.error);
      return false;
    }
  } catch (error) {
    console.error("❌ 无法连接到API服务:", error.message);
    return false;
  }
}

/**
 * 启动定时任务
 */
async function startCronJob() {
  console.log("🚀 AI-B 定时分析 Cron 脚本启动");
  console.log(`📍 API地址: ${CONFIG.API_BASE_URL}`);
  console.log(`⏰ 执行频率: 每10分钟`);
  console.log(`🌍 时区: ${CONFIG.TIMEZONE}`);
  console.log("");

  // 检查API服务状态
  console.log("🔍 检查API服务状态...");
  const apiOk = await checkApiStatus();

  if (!apiOk) {
    console.error("❌ API服务不可用，请检查服务是否正常运行");
    process.exit(1);
  }

  console.log("");
  console.log("⏰ 启动定时任务...");

  // 创建定时任务
  const task = cron.schedule(CONFIG.CRON_EXPRESSION, executeAnalysis, {
    scheduled: true,
    timezone: CONFIG.TIMEZONE,
  });

  console.log("✅ 定时任务已启动");
  console.log("📝 日志格式: [时间戳] 状态 消息");
  console.log("");

  // 启动时默认执行一次分析
  console.log("🔄 启动时执行一次分析...");
  await executeAnalysis();
  console.log("");

  // 优雅关闭处理
  process.on("SIGINT", () => {
    console.log("\n🛑 收到停止信号，正在关闭定时任务...");
    task.stop();
    console.log("✅ 定时任务已停止");
    process.exit(0);
  });

  process.on("SIGTERM", () => {
    console.log("\n🛑 收到终止信号，正在关闭定时任务...");
    task.stop();
    console.log("✅ 定时任务已停止");
    process.exit(0);
  });

  // 保持进程运行
  console.log("🔄 定时任务运行中... (按 Ctrl+C 停止)");
}

// 主函数
if (require.main === module) {
  startCronJob().catch((error) => {
    console.error("❌ 启动失败:", error);
    process.exit(1);
  });
}

module.exports = {
  executeAnalysis,
  checkApiStatus,
  startCronJob,
  CONFIG,
};
