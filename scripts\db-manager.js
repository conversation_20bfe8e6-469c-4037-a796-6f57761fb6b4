#!/usr/bin/env node

/**
 * 数据库管理脚本 - 提供数据库维护和统计功能
 */

const { PrismaClient } = require('@prisma/client')

const prisma = new PrismaClient()

// 命令行参数
const command = process.argv[2]

async function main() {
  console.log('🗄️ AI-B 数据库管理工具')
  console.log('')

  switch (command) {
    case 'stats':
      await showStats()
      break
    case 'cleanup':
      await cleanupData()
      break
    case 'validate':
      await validatePredictions()
      break
    case 'quality':
      await showQualityStats()
      break
    case 'export':
      await exportData()
      break
    default:
      showHelp()
  }
}

async function showStats() {
  console.log('📊 数据库统计信息:')
  console.log('')

  try {
    // 预测记录统计
    const totalPredictions = await prisma.predictionRecord.count()
    const verifiedPredictions = await prisma.predictionRecord.count({
      where: { verifiedAt: { not: null } }
    })
    const pendingPredictions = totalPredictions - verifiedPredictions

    console.log(`📈 预测记录:`)
    console.log(`  总数: ${totalPredictions}`)
    console.log(`  已验证: ${verifiedPredictions}`)
    console.log(`  待验证: ${pendingPredictions}`)
    console.log('')

    // 分析结果统计
    const totalAnalysis = await prisma.analysisResult.count()
    console.log(`🔍 分析结果: ${totalAnalysis}`)
    console.log('')

    // 数据质量记录统计
    const totalQualityRecords = await prisma.dataQualityRecord.count()
    const validQualityRecords = await prisma.dataQualityRecord.count({
      where: { isValid: true }
    })
    
    console.log(`🎯 数据质量记录:`)
    console.log(`  总数: ${totalQualityRecords}`)
    console.log(`  有效: ${validQualityRecords}`)
    console.log(`  有效率: ${totalQualityRecords > 0 ? ((validQualityRecords / totalQualityRecords) * 100).toFixed(1) : 0}%`)
    console.log('')

    // 验证任务统计
    const pendingTasks = await prisma.validationTask.count({
      where: { status: 'PENDING' }
    })
    const completedTasks = await prisma.validationTask.count({
      where: { status: 'COMPLETED' }
    })
    const failedTasks = await prisma.validationTask.count({
      where: { status: 'FAILED' }
    })

    console.log(`⏰ 验证任务:`)
    console.log(`  待处理: ${pendingTasks}`)
    console.log(`  已完成: ${completedTasks}`)
    console.log(`  失败: ${failedTasks}`)
    console.log('')

    // 最近活动
    const recentPredictions = await prisma.predictionRecord.count({
      where: {
        timestamp: {
          gte: new Date(Date.now() - 24 * 60 * 60 * 1000) // 最近24小时
        }
      }
    })

    console.log(`🕐 最近24小时活动:`)
    console.log(`  新预测: ${recentPredictions}`)

  } catch (error) {
    console.error('❌ 获取统计信息失败:', error)
  }
}

async function cleanupData() {
  console.log('🧹 开始清理过期数据...')
  console.log('')

  try {
    const retentionDays = 90
    const cutoffDate = new Date()
    cutoffDate.setDate(cutoffDate.getDate() - retentionDays)

    console.log(`📅 清理 ${retentionDays} 天前的数据 (${cutoffDate.toISOString()})`)
    console.log('')

    // 清理预测记录
    const deletedPredictions = await prisma.predictionRecord.deleteMany({
      where: {
        timestamp: { lt: cutoffDate }
      }
    })

    // 清理分析结果
    const deletedAnalysis = await prisma.analysisResult.deleteMany({
      where: {
        timestamp: { lt: cutoffDate }
      }
    })

    // 清理数据质量记录
    const deletedQuality = await prisma.dataQualityRecord.deleteMany({
      where: {
        timestamp: { lt: cutoffDate }
      }
    })

    // 清理完成的验证任务
    const deletedTasks = await prisma.validationTask.deleteMany({
      where: {
        createdAt: { lt: cutoffDate },
        status: 'COMPLETED'
      }
    })

    console.log('✅ 清理完成:')
    console.log(`  预测记录: ${deletedPredictions.count}`)
    console.log(`  分析结果: ${deletedAnalysis.count}`)
    console.log(`  数据质量记录: ${deletedQuality.count}`)
    console.log(`  验证任务: ${deletedTasks.count}`)

  } catch (error) {
    console.error('❌ 数据清理失败:', error)
  }
}

async function validatePredictions() {
  console.log('🔍 开始验证待处理的预测...')
  console.log('')

  try {
    const pendingTasks = await prisma.validationTask.findMany({
      where: {
        status: 'PENDING',
        scheduledAt: { lte: new Date() }
      },
      take: 10
    })

    console.log(`发现 ${pendingTasks.length} 个待验证的预测`)

    if (pendingTasks.length === 0) {
      console.log('✅ 没有待验证的预测')
      return
    }

    // 这里应该调用实际的验证逻辑
    console.log('💡 提示: 请使用 API 接口进行实际验证')
    console.log('  POST /api/predictions/validate')
    console.log('  { "batchValidate": true }')

  } catch (error) {
    console.error('❌ 验证预测失败:', error)
  }
}

async function showQualityStats() {
  console.log('🎯 数据质量统计:')
  console.log('')

  try {
    const last7Days = new Date()
    last7Days.setDate(last7Days.getDate() - 7)

    const qualityRecords = await prisma.dataQualityRecord.findMany({
      where: {
        timestamp: { gte: last7Days }
      },
      orderBy: { timestamp: 'desc' }
    })

    if (qualityRecords.length === 0) {
      console.log('📭 最近7天没有数据质量记录')
      return
    }

    const avgScore = qualityRecords.reduce((sum, r) => sum + r.score, 0) / qualityRecords.length
    const validCount = qualityRecords.filter(r => r.isValid).length
    const validRate = (validCount / qualityRecords.length) * 100

    console.log(`📊 最近7天质量统计:`)
    console.log(`  记录数: ${qualityRecords.length}`)
    console.log(`  平均评分: ${avgScore.toFixed(1)}/100`)
    console.log(`  有效率: ${validRate.toFixed(1)}%`)
    console.log('')

    // 按币种统计
    const bySymbol = {}
    qualityRecords.forEach(r => {
      if (!bySymbol[r.symbol]) {
        bySymbol[r.symbol] = { count: 0, totalScore: 0 }
      }
      bySymbol[r.symbol].count++
      bySymbol[r.symbol].totalScore += r.score
    })

    console.log('📈 按币种统计:')
    Object.entries(bySymbol).forEach(([symbol, stats]) => {
      const avgScore = stats.totalScore / stats.count
      console.log(`  ${symbol}: ${stats.count} 次检查, 平均 ${avgScore.toFixed(1)} 分`)
    })

  } catch (error) {
    console.error('❌ 获取质量统计失败:', error)
  }
}

async function exportData() {
  console.log('📤 导出数据功能开发中...')
  console.log('💡 提示: 可以使用 Prisma Studio 查看和导出数据')
  console.log('  运行: npm run db:studio')
}

function showHelp() {
  console.log('📖 使用说明:')
  console.log('')
  console.log('可用命令:')
  console.log('  stats     - 显示数据库统计信息')
  console.log('  cleanup   - 清理过期数据')
  console.log('  validate  - 验证待处理的预测')
  console.log('  quality   - 显示数据质量统计')
  console.log('  export    - 导出数据 (开发中)')
  console.log('')
  console.log('示例:')
  console.log('  node scripts/db-manager.js stats')
  console.log('  node scripts/db-manager.js cleanup')
  console.log('')
}

// 优雅关闭
async function cleanup() {
  await prisma.$disconnect()
}

// 主函数
if (require.main === module) {
  main()
    .then(() => {
      console.log('')
      console.log('✅ 操作完成')
    })
    .catch((error) => {
      console.error('')
      console.error('❌ 操作失败:', error)
      process.exit(1)
    })
    .finally(cleanup)
}

module.exports = {
  showStats,
  cleanupData,
  validatePredictions,
  showQualityStats
}
