import Big from 'big.js';

// 配置 Big.js 的精度和舍入模式
Big.DP = 20; // 设置小数位数
Big.RM = Big.roundHalfUp; // 设置舍入模式

/**
 * 安全地将值转换为 Big 实例
 */
export function toBig(value: number | string | Big): Big {
  if (value instanceof Big) {
    return value;
  }
  if (typeof value === 'string' && value.trim() === '') {
    return new Big(0);
  }
  try {
    return new Big(value);
  } catch (error) {
    console.warn(`Failed to convert ${value} to Big, using 0 instead:`, error);
    return new Big(0);
  }
}

/**
 * 安全地将 Big 实例转换为数字
 */
export function toNumber(value: Big | number | string): number {
  if (typeof value === 'number') {
    return value;
  }
  if (typeof value === 'string') {
    return parseFloat(value) || 0;
  }
  try {
    return value.toNumber();
  } catch (error) {
    console.warn(`Failed to convert Big to number:`, error);
    return 0;
  }
}

/**
 * 格式化价格显示
 */
export function formatPrice(price: number | string | Big): string {
  const bigPrice = toBig(price);
  
  if (bigPrice.gte(1000)) {
    return bigPrice.toFixed(2);
  } else if (bigPrice.gte(1)) {
    return bigPrice.toFixed(4);
  } else if (bigPrice.gte(0.01)) {
    return bigPrice.toFixed(6);
  } else {
    return bigPrice.toFixed(8);
  }
}

/**
 * 格式化百分比显示
 */
export function formatPercentage(value: number | string | Big, decimals: number = 2): string {
  const bigValue = toBig(value);
  return bigValue.toFixed(decimals);
}

/**
 * 格式化数字显示
 */
export function formatNumber(value: number | string | Big, decimals: number = 2): string {
  const bigValue = toBig(value);
  return bigValue.toFixed(decimals);
}

/**
 * 格式化成交量显示
 */
export function formatVolume(volume: number | string | Big): string {
  const bigVolume = toBig(volume);
  
  if (bigVolume.gte(1000000)) {
    return `${bigVolume.div(1000000).toFixed(1)}M`;
  } else if (bigVolume.gte(1000)) {
    return `${bigVolume.div(1000).toFixed(1)}K`;
  } else {
    return bigVolume.toFixed(0);
  }
}

/**
 * 安全的加法运算
 */
export function add(a: number | string | Big, b: number | string | Big): Big {
  return toBig(a).plus(toBig(b));
}

/**
 * 安全的减法运算
 */
export function subtract(a: number | string | Big, b: number | string | Big): Big {
  return toBig(a).minus(toBig(b));
}

/**
 * 安全的乘法运算
 */
export function multiply(a: number | string | Big, b: number | string | Big): Big {
  return toBig(a).times(toBig(b));
}

/**
 * 安全的除法运算
 */
export function divide(a: number | string | Big, b: number | string | Big): Big {
  const divisor = toBig(b);
  if (divisor.eq(0)) {
    console.warn('Division by zero, returning 0');
    return new Big(0);
  }
  return toBig(a).div(divisor);
}

/**
 * 计算百分比变化
 */
export function calculatePercentageChange(oldValue: number | string | Big, newValue: number | string | Big): Big {
  const oldBig = toBig(oldValue);
  const newBig = toBig(newValue);
  
  if (oldBig.eq(0)) {
    return new Big(0);
  }
  
  return newBig.minus(oldBig).div(oldBig).times(100);
}

/**
 * 计算风险收益比
 */
export function calculateRiskRewardRatio(entryPrice: number | string | Big, stopLoss: number | string | Big, takeProfit: number | string | Big): Big {
  const entry = toBig(entryPrice);
  const stop = toBig(stopLoss);
  const profit = toBig(takeProfit);
  
  const risk = entry.minus(stop).abs();
  const reward = profit.minus(entry).abs();
  
  if (risk.eq(0)) {
    return new Big(0);
  }
  
  return reward.div(risk);
}

/**
 * 格式化价格用于本地化显示
 */
export function formatPriceLocale(price: number | string | Big): string {
  const bigPrice = toBig(price);
  const numPrice = toNumber(bigPrice);
  
  if (bigPrice.gte(1000)) {
    return numPrice.toLocaleString("en-US", {
      minimumFractionDigits: 2,
      maximumFractionDigits: 2,
    });
  } else if (bigPrice.gte(1)) {
    return formatPrice(bigPrice);
  } else {
    return formatPrice(bigPrice);
  }
}

/**
 * 比较两个数值
 */
export function compare(a: number | string | Big, b: number | string | Big): number {
  const bigA = toBig(a);
  const bigB = toBig(b);
  return bigA.cmp(bigB);
}

/**
 * 检查数值是否大于等于另一个数值
 */
export function gte(a: number | string | Big, b: number | string | Big): boolean {
  return toBig(a).gte(toBig(b));
}

/**
 * 检查数值是否小于等于另一个数值
 */
export function lte(a: number | string | Big, b: number | string | Big): boolean {
  return toBig(a).lte(toBig(b));
}

/**
 * 检查数值是否大于另一个数值
 */
export function gt(a: number | string | Big, b: number | string | Big): boolean {
  return toBig(a).gt(toBig(b));
}

/**
 * 检查数值是否小于另一个数值
 */
export function lt(a: number | string | Big, b: number | string | Big): boolean {
  return toBig(a).lt(toBig(b));
}

/**
 * 检查数值是否等于另一个数值
 */
export function eq(a: number | string | Big, b: number | string | Big): boolean {
  return toBig(a).eq(toBig(b));
}

/**
 * 获取绝对值
 */
export function abs(value: number | string | Big): Big {
  return toBig(value).abs();
}

/**
 * 获取最大值
 */
export function max(a: number | string | Big, b: number | string | Big): Big {
  const bigA = toBig(a);
  const bigB = toBig(b);
  return bigA.gt(bigB) ? bigA : bigB;
}

/**
 * 获取最小值
 */
export function min(a: number | string | Big, b: number | string | Big): Big {
  const bigA = toBig(a);
  const bigB = toBig(b);
  return bigA.lt(bigB) ? bigA : bigB;
}
