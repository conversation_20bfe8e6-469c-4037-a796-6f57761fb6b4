import { ProcessedKlineData, MarketData } from "@/types/trading";

export interface HighFrequencySignal {
  type: "SCALPING" | "MOMENTUM" | "REVERSAL" | "BREAKOUT";
  direction: "BUY" | "SELL";
  strength: number; // 1-10
  timeframe: "1MIN" | "5MIN" | "15MIN";
  entryPrice: number;
  stopLoss: number;
  takeProfit: number[];
  confidence: number;
  reasoning: string;
  expiryTime: number; // 信号过期时间
}

export interface ScalpingOpportunity {
  signals: HighFrequencySignal[];
  marketCondition: "FAVORABLE" | "NEUTRAL" | "UNFAVORABLE";
  riskLevel: "LOW" | "MEDIUM" | "HIGH";
  expectedDuration: number; // 预期持仓时间（分钟）
  profitPotential: number; // 预期利润率
}

export class HighFrequencySignalDetector {
  private static instance: HighFrequencySignalDetector;

  static getInstance(): HighFrequencySignalDetector {
    if (!HighFrequencySignalDetector.instance) {
      HighFrequencySignalDetector.instance = new HighFrequencySignalDetector();
    }
    return HighFrequencySignalDetector.instance;
  }

  /**
   * 检测高频交易机会
   */
  detectScalpingOpportunities(marketData: MarketData): ScalpingOpportunity {
    const { oneMin, thirtyMin } = marketData;
    const signals: HighFrequencySignal[] = [];

    // 检测1分钟剥头皮信号
    const scalpingSignals = this.detectScalpingSignals(oneMin);
    signals.push(...scalpingSignals);

    // 检测动量突破信号
    const momentumSignals = this.detectMomentumSignals(oneMin);
    signals.push(...momentumSignals);

    // 检测反转信号
    const reversalSignals = this.detectReversalSignals(oneMin);
    signals.push(...reversalSignals);

    // 检测突破信号
    const breakoutSignals = this.detectBreakoutSignals(thirtyMin);
    signals.push(...breakoutSignals);

    // 评估市场条件
    const marketCondition = this.evaluateMarketCondition(oneMin);
    
    // 评估风险等级
    const riskLevel = this.assessRiskLevel(oneMin);

    // 计算预期持仓时间和利润潜力
    const expectedDuration = this.calculateExpectedDuration(signals);
    const profitPotential = this.calculateProfitPotential(signals);

    return {
      signals: signals.sort((a, b) => b.confidence - a.confidence),
      marketCondition,
      riskLevel,
      expectedDuration,
      profitPotential,
    };
  }

  /**
   * 检测剥头皮信号
   */
  private detectScalpingSignals(data: ProcessedKlineData[]): HighFrequencySignal[] {
    if (data.length < 20) return [];

    const signals: HighFrequencySignal[] = [];
    const recent = data.slice(-20);
    const currentPrice = recent[recent.length - 1].close;

    // 检测价格压缩后的突破
    const priceRange = this.calculatePriceRange(recent.slice(-10));
    const avgRange = this.calculateAverageRange(recent.slice(-20, -10));

    if (priceRange < avgRange * 0.6) { // 价格压缩
      const volumeSpike = this.detectVolumeSpike(recent);
      
      if (volumeSpike) {
        const direction = recent[recent.length - 1].changePercent > 0 ? "BUY" : "SELL";
        const stopDistance = priceRange * 0.5;
        
        signals.push({
          type: "SCALPING",
          direction,
          strength: 7,
          timeframe: "1MIN",
          entryPrice: currentPrice,
          stopLoss: direction === "BUY" ? currentPrice - stopDistance : currentPrice + stopDistance,
          takeProfit: [
            direction === "BUY" ? currentPrice + stopDistance * 2 : currentPrice - stopDistance * 2
          ],
          confidence: 75,
          reasoning: "价格压缩后成交量突破，适合剥头皮交易",
          expiryTime: Date.now() + 5 * 60 * 1000, // 5分钟后过期
        });
      }
    }

    return signals;
  }

  /**
   * 检测动量信号
   */
  private detectMomentumSignals(data: ProcessedKlineData[]): HighFrequencySignal[] {
    if (data.length < 15) return [];

    const signals: HighFrequencySignal[] = [];
    const recent = data.slice(-15);
    const currentPrice = recent[recent.length - 1].close;

    // 检测连续同向K线
    const consecutiveBars = this.countConsecutiveBars(recent.slice(-5));
    
    if (Math.abs(consecutiveBars) >= 3) {
      const direction = consecutiveBars > 0 ? "BUY" : "SELL";
      const momentum = this.calculateMomentum(recent);
      
      if (momentum > 0.5) { // 动量足够强
        const atr = this.calculateATR(recent);
        
        signals.push({
          type: "MOMENTUM",
          direction,
          strength: Math.min(10, Math.abs(consecutiveBars) + 5),
          timeframe: "1MIN",
          entryPrice: currentPrice,
          stopLoss: direction === "BUY" ? currentPrice - atr : currentPrice + atr,
          takeProfit: [
            direction === "BUY" ? currentPrice + atr * 2 : currentPrice - atr * 2,
            direction === "BUY" ? currentPrice + atr * 3 : currentPrice - atr * 3
          ],
          confidence: 70 + momentum * 20,
          reasoning: `连续${Math.abs(consecutiveBars)}根${direction === "BUY" ? "阳" : "阴"}线，动量强劲`,
          expiryTime: Date.now() + 10 * 60 * 1000, // 10分钟后过期
        });
      }
    }

    return signals;
  }

  /**
   * 检测反转信号
   */
  private detectReversalSignals(data: ProcessedKlineData[]): HighFrequencySignal[] {
    if (data.length < 10) return [];

    const signals: HighFrequencySignal[] = [];
    const recent = data.slice(-10);
    const currentPrice = recent[recent.length - 1].close;

    // 检测锤子线/上吊线
    const lastBar = recent[recent.length - 1];
    const isHammer = this.isHammerPattern(lastBar);
    const isShootingStar = this.isShootingStarPattern(lastBar);

    if (isHammer || isShootingStar) {
      const direction = isHammer ? "BUY" : "SELL";
      const bodySize = Math.abs(lastBar.close - lastBar.open);
      const atr = this.calculateATR(recent);

      signals.push({
        type: "REVERSAL",
        direction,
        strength: 6,
        timeframe: "1MIN",
        entryPrice: currentPrice,
        stopLoss: direction === "BUY" ? lastBar.low - bodySize : lastBar.high + bodySize,
        takeProfit: [
          direction === "BUY" ? currentPrice + atr * 1.5 : currentPrice - atr * 1.5
        ],
        confidence: 65,
        reasoning: `检测到${isHammer ? "锤子线" : "流星线"}反转形态`,
        expiryTime: Date.now() + 15 * 60 * 1000, // 15分钟后过期
      });
    }

    return signals;
  }

  /**
   * 检测突破信号
   */
  private detectBreakoutSignals(data: ProcessedKlineData[]): HighFrequencySignal[] {
    if (data.length < 20) return [];

    const signals: HighFrequencySignal[] = [];
    const recent = data.slice(-20);
    const currentPrice = recent[recent.length - 1].close;

    // 计算支撑阻力位
    const resistance = Math.max(...recent.slice(-10).map(d => d.high));
    const support = Math.min(...recent.slice(-10).map(d => d.low));

    // 检测突破
    if (currentPrice > resistance * 1.001) { // 向上突破
      const volumeConfirmation = this.checkVolumeConfirmation(recent);
      
      if (volumeConfirmation) {
        signals.push({
          type: "BREAKOUT",
          direction: "BUY",
          strength: 8,
          timeframe: "5MIN",
          entryPrice: currentPrice,
          stopLoss: resistance * 0.998,
          takeProfit: [
            currentPrice + (currentPrice - resistance) * 1.5,
            currentPrice + (currentPrice - resistance) * 2.5
          ],
          confidence: 80,
          reasoning: "向上突破阻力位，成交量确认",
          expiryTime: Date.now() + 30 * 60 * 1000, // 30分钟后过期
        });
      }
    } else if (currentPrice < support * 0.999) { // 向下突破
      const volumeConfirmation = this.checkVolumeConfirmation(recent);
      
      if (volumeConfirmation) {
        signals.push({
          type: "BREAKOUT",
          direction: "SELL",
          strength: 8,
          timeframe: "5MIN",
          entryPrice: currentPrice,
          stopLoss: support * 1.002,
          takeProfit: [
            currentPrice - (support - currentPrice) * 1.5,
            currentPrice - (support - currentPrice) * 2.5
          ],
          confidence: 80,
          reasoning: "向下突破支撑位，成交量确认",
          expiryTime: Date.now() + 30 * 60 * 1000, // 30分钟后过期
        });
      }
    }

    return signals;
  }

  /**
   * 评估市场条件
   */
  private evaluateMarketCondition(data: ProcessedKlineData[]): ScalpingOpportunity["marketCondition"] {
    if (data.length < 30) return "NEUTRAL";

    const recent = data.slice(-30);
    const volatility = this.calculateVolatility(recent);
    const avgVolume = recent.reduce((sum, d) => sum + d.volume, 0) / recent.length;
    const recentVolume = recent.slice(-5).reduce((sum, d) => sum + d.volume, 0) / 5;

    // 适度波动率和充足流动性有利于剥头皮
    if (volatility > 0.1 && volatility < 0.4 && recentVolume > avgVolume * 0.8) {
      return "FAVORABLE";
    }

    // 极低或极高波动率不利于剥头皮
    if (volatility < 0.05 || volatility > 0.6) {
      return "UNFAVORABLE";
    }

    return "NEUTRAL";
  }

  /**
   * 评估风险等级
   */
  private assessRiskLevel(data: ProcessedKlineData[]): ScalpingOpportunity["riskLevel"] {
    if (data.length < 20) return "MEDIUM";

    const recent = data.slice(-20);
    const volatility = this.calculateVolatility(recent);
    const priceGaps = this.calculatePriceGaps(recent);

    if (volatility > 0.5 || priceGaps > 0.02) {
      return "HIGH";
    }

    if (volatility < 0.15 && priceGaps < 0.005) {
      return "LOW";
    }

    return "MEDIUM";
  }

  // 辅助方法
  private calculatePriceRange(data: ProcessedKlineData[]): number {
    const high = Math.max(...data.map(d => d.high));
    const low = Math.min(...data.map(d => d.low));
    return high - low;
  }

  private calculateAverageRange(data: ProcessedKlineData[]): number {
    return data.reduce((sum, d) => sum + (d.high - d.low), 0) / data.length;
  }

  private detectVolumeSpike(data: ProcessedKlineData[]): boolean {
    if (data.length < 5) return false;
    const avgVolume = data.slice(0, -1).reduce((sum, d) => sum + d.volume, 0) / (data.length - 1);
    const currentVolume = data[data.length - 1].volume;
    return currentVolume > avgVolume * 1.5;
  }

  private countConsecutiveBars(data: ProcessedKlineData[]): number {
    let count = 0;
    const direction = data[data.length - 1].changePercent > 0 ? 1 : -1;
    
    for (let i = data.length - 1; i >= 0; i--) {
      if ((data[i].changePercent > 0 ? 1 : -1) === direction) {
        count += direction;
      } else {
        break;
      }
    }
    
    return count;
  }

  private calculateMomentum(data: ProcessedKlineData[]): number {
    if (data.length < 5) return 0;
    const priceChange = (data[data.length - 1].close - data[0].close) / data[0].close;
    return Math.abs(priceChange) * 10; // 归一化到0-1范围
  }

  private calculateATR(data: ProcessedKlineData[], period: number = 14): number {
    if (data.length < period + 1) return 0;
    
    const trueRanges = [];
    for (let i = 1; i < data.length; i++) {
      const high = data[i].high;
      const low = data[i].low;
      const prevClose = data[i - 1].close;
      
      const tr = Math.max(
        high - low,
        Math.abs(high - prevClose),
        Math.abs(low - prevClose)
      );
      trueRanges.push(tr);
    }
    
    return trueRanges.slice(-period).reduce((sum, tr) => sum + tr, 0) / period;
  }

  private isHammerPattern(bar: ProcessedKlineData): boolean {
    const bodySize = Math.abs(bar.close - bar.open);
    const lowerShadow = Math.min(bar.open, bar.close) - bar.low;
    const upperShadow = bar.high - Math.max(bar.open, bar.close);
    
    return lowerShadow > bodySize * 2 && upperShadow < bodySize * 0.5;
  }

  private isShootingStarPattern(bar: ProcessedKlineData): boolean {
    const bodySize = Math.abs(bar.close - bar.open);
    const lowerShadow = Math.min(bar.open, bar.close) - bar.low;
    const upperShadow = bar.high - Math.max(bar.open, bar.close);
    
    return upperShadow > bodySize * 2 && lowerShadow < bodySize * 0.5;
  }

  private checkVolumeConfirmation(data: ProcessedKlineData[]): boolean {
    if (data.length < 5) return false;
    const avgVolume = data.slice(0, -1).reduce((sum, d) => sum + d.volume, 0) / (data.length - 1);
    const currentVolume = data[data.length - 1].volume;
    return currentVolume > avgVolume * 1.2;
  }

  private calculateVolatility(data: ProcessedKlineData[]): number {
    if (data.length < 2) return 0;
    const returns = data.slice(1).map((item, index) => 
      Math.log(item.close / data[index].close)
    );
    const mean = returns.reduce((sum, r) => sum + r, 0) / returns.length;
    const variance = returns.reduce((sum, r) => sum + Math.pow(r - mean, 2), 0) / returns.length;
    return Math.sqrt(variance);
  }

  private calculatePriceGaps(data: ProcessedKlineData[]): number {
    if (data.length < 2) return 0;
    let totalGap = 0;
    for (let i = 1; i < data.length; i++) {
      const gap = Math.abs(data[i].open - data[i - 1].close) / data[i - 1].close;
      totalGap += gap;
    }
    return totalGap / (data.length - 1);
  }

  private calculateExpectedDuration(signals: HighFrequencySignal[]): number {
    if (signals.length === 0) return 0;
    const durations = signals.map(s => {
      switch (s.type) {
        case "SCALPING": return 5;
        case "MOMENTUM": return 15;
        case "REVERSAL": return 20;
        case "BREAKOUT": return 30;
        default: return 10;
      }
    });
    return durations.reduce((sum, d) => sum + d, 0) / durations.length;
  }

  private calculateProfitPotential(signals: HighFrequencySignal[]): number {
    if (signals.length === 0) return 0;
    const potentials = signals.map(s => {
      const entryPrice = s.entryPrice;
      const firstTarget = s.takeProfit[0];
      return Math.abs(firstTarget - entryPrice) / entryPrice;
    });
    return potentials.reduce((sum, p) => sum + p, 0) / potentials.length;
  }
}

export const highFrequencySignalDetector = HighFrequencySignalDetector.getInstance();
