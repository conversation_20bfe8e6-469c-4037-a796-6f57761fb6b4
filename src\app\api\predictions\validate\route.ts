/**
 * 预测验证API接口
 */

import { NextRequest, NextResponse } from "next/server";
import { serverPredictionTracker } from "@/lib/server-prediction-tracker";
import { initializeDatabase } from "@/lib/database";
import {
  FeishuWebhookService,
  PredictionValidationReport,
} from "@/lib/feishu-webhook";
import { schedulerConfigService } from "@/lib/scheduler-config";

export async function POST(request: NextRequest) {
  try {
    // 初始化数据库连接
    await initializeDatabase();

    const body = await request.json();
    const { predictionId, batchValidate } = body;

    if (batchValidate) {
      // 批量验证
      console.log("🔄 开始批量验证预测...");
      const verifiedCount =
        await serverPredictionTracker.batchVerifyPredictions();

      // 获取验证后的统计数据
      const stats = await serverPredictionTracker.getAccuracyStats();
      const suggestions =
        await serverPredictionTracker.getImprovementSuggestions();

      // 如果有验证结果且飞书服务可用，发送报告到飞书
      if (verifiedCount > 0) {
        try {
          const config = schedulerConfigService.getConfig();
          if (config.feishu.enabled && config.feishu.webhookUrl) {
            const feishuService = new FeishuWebhookService(
              config.feishu.webhookUrl
            );

            // 获取详细统计信息
            const detailedStats =
              await serverPredictionTracker.getDetailedStats();

            const report: PredictionValidationReport = {
              verifiedCount,
              totalPredictions: stats.totalPredictions,
              verifiedPredictions: stats.verifiedPredictions,
              directionAccuracy: stats.directionAccuracy,
              priceAccuracy: stats.priceAccuracy,
              overallAccuracy: stats.overallAccuracy,
              bySymbol: stats.bySymbol,
              byTimeframe: stats.byTimeframe,
              byConfidence: stats.byConfidence,
              recentTrend: stats.recentTrend,
              suggestions,
              timestamp: new Date().toLocaleString("zh-CN"),
              detailedStats, // 添加详细统计信息
            };

            console.log("📊 发送验证报告到飞书...");
            const sendResult = await feishuService.sendValidationReport(
              report,
              false
            );
            console.log(`验证报告发送结果: ${sendResult ? "成功" : "失败"}`);
          }
        } catch (feishuError) {
          console.error("发送验证报告到飞书失败:", feishuError);
        }
      }

      return NextResponse.json({
        success: true,
        message: `批量验证完成，验证了 ${verifiedCount} 个预测`,
        data: { verifiedCount, stats, suggestions },
      });
    } else if (predictionId) {
      // 验证单个预测
      console.log(`🔄 验证预测: ${predictionId}`);
      await serverPredictionTracker.verifyPrediction(predictionId);

      return NextResponse.json({
        success: true,
        message: `预测 ${predictionId} 验证完成`,
      });
    } else {
      return NextResponse.json(
        {
          success: false,
          error: "缺少必要参数",
        },
        { status: 400 }
      );
    }
  } catch (error) {
    console.error("预测验证失败:", error);
    return NextResponse.json(
      {
        success: false,
        error: error instanceof Error ? error.message : "验证失败",
      },
      { status: 500 }
    );
  }
}

export async function GET(request: NextRequest) {
  try {
    // 初始化数据库连接
    await initializeDatabase();

    const { searchParams } = new URL(request.url);
    const symbol = searchParams.get("symbol") || undefined;
    const timeframe = searchParams.get("timeframe") || undefined;

    // 获取准确率统计
    const stats = await serverPredictionTracker.getAccuracyStats(
      symbol,
      timeframe
    );

    // 获取改进建议
    const suggestions =
      await serverPredictionTracker.getImprovementSuggestions();

    return NextResponse.json({
      success: true,
      data: {
        stats,
        suggestions,
      },
    });
  } catch (error) {
    console.error("获取预测统计失败:", error);
    return NextResponse.json(
      {
        success: false,
        error: error instanceof Error ? error.message : "获取统计失败",
      },
      { status: 500 }
    );
  }
}
