/**
 * 范围验证配置
 * 为不同时间框架提供推荐的验证参数
 */

export interface RangeValidationConfig {
  enabled: boolean;
  samplingInterval: number; // 采样间隔（分钟）
  tolerancePercent: number; // 价格容忍度百分比
  description: string;
}

export interface TimeframeConfig {
  [timeframe: string]: RangeValidationConfig;
}

/**
 * 预定义的时间框架配置
 */
export const TIMEFRAME_CONFIGS: TimeframeConfig = {
  // 极短期交易 (15分钟 - 2小时)
  "15分钟": {
    enabled: true,
    samplingInterval: 1,      // 每1分钟采样
    tolerancePercent: 0.1,    // 0.1% 容忍度
    description: "极短期高频采样，适合快速交易"
  },
  
  "30分钟": {
    enabled: true,
    samplingInterval: 2,      // 每2分钟采样
    tolerancePercent: 0.2,    // 0.2% 容忍度
    description: "短期密集采样，捕捉快速价格变动"
  },
  
  "1小时": {
    enabled: true,
    samplingInterval: 3,      // 每3分钟采样
    tolerancePercent: 0.3,    // 0.3% 容忍度
    description: "小时级交易的标准配置"
  },
  
  "2小时": {
    enabled: true,
    samplingInterval: 5,      // 每5分钟采样
    tolerancePercent: 0.4,    // 0.4% 容忍度
    description: "短期交易的平衡配置"
  },
  
  // 短期交易 (2-8小时)
  "4小时": {
    enabled: true,
    samplingInterval: 10,     // 每10分钟采样
    tolerancePercent: 0.5,    // 0.5% 容忍度
    description: "中短期交易配置"
  },
  
  "8小时": {
    enabled: true,
    samplingInterval: 15,     // 每15分钟采样
    tolerancePercent: 0.6,    // 0.6% 容忍度
    description: "短期交易的宽松配置"
  },
  
  // 中期交易 (8-24小时)
  "12小时": {
    enabled: true,
    samplingInterval: 20,     // 每20分钟采样
    tolerancePercent: 0.8,    // 0.8% 容忍度
    description: "中期交易标准配置"
  },
  
  "24小时": {
    enabled: true,
    samplingInterval: 30,     // 每30分钟采样
    tolerancePercent: 1.0,    // 1.0% 容忍度
    description: "日级交易配置"
  },
  
  // 复合时间框架
  "15分钟到2小时": {
    enabled: true,
    samplingInterval: 5,      // 每5分钟采样
    tolerancePercent: 0.5,    // 0.5% 容忍度
    description: "极短期范围交易，高频监控"
  },
  
  "1小时到4小时": {
    enabled: true,
    samplingInterval: 8,      // 每8分钟采样
    tolerancePercent: 0.6,    // 0.6% 容忍度
    description: "短期范围交易配置"
  },
  
  "4小时到8小时": {
    enabled: true,
    samplingInterval: 12,     // 每12分钟采样
    tolerancePercent: 0.7,    // 0.7% 容忍度
    description: "中短期范围交易配置"
  }
};

/**
 * 根据时间框架获取推荐配置
 */
export function getRecommendedConfig(timeframe: string): RangeValidationConfig {
  // 直接匹配
  if (TIMEFRAME_CONFIGS[timeframe]) {
    return TIMEFRAME_CONFIGS[timeframe];
  }
  
  // 模糊匹配
  const lowerTimeframe = timeframe.toLowerCase();
  
  if (lowerTimeframe.includes("15分钟") || lowerTimeframe.includes("15min")) {
    return TIMEFRAME_CONFIGS["15分钟"];
  }
  
  if (lowerTimeframe.includes("30分钟") || lowerTimeframe.includes("30min")) {
    return TIMEFRAME_CONFIGS["30分钟"];
  }
  
  if (lowerTimeframe.includes("1小时") || lowerTimeframe.includes("1h")) {
    return TIMEFRAME_CONFIGS["1小时"];
  }
  
  if (lowerTimeframe.includes("2小时") || lowerTimeframe.includes("2h")) {
    return TIMEFRAME_CONFIGS["2小时"];
  }
  
  if (lowerTimeframe.includes("4小时") || lowerTimeframe.includes("4h")) {
    return TIMEFRAME_CONFIGS["4小时"];
  }
  
  if (lowerTimeframe.includes("8小时") || lowerTimeframe.includes("8h")) {
    return TIMEFRAME_CONFIGS["8小时"];
  }
  
  if (lowerTimeframe.includes("12小时") || lowerTimeframe.includes("12h")) {
    return TIMEFRAME_CONFIGS["12小时"];
  }
  
  if (lowerTimeframe.includes("24小时") || lowerTimeframe.includes("24h") || lowerTimeframe.includes("1d")) {
    return TIMEFRAME_CONFIGS["24小时"];
  }
  
  // 默认配置
  return {
    enabled: true,
    samplingInterval: 10,
    tolerancePercent: 0.5,
    description: "默认范围验证配置"
  };
}

/**
 * 根据交易类型获取配置
 */
export function getConfigByTradingType(tradingType: 'scalping' | 'day' | 'swing' | 'position'): RangeValidationConfig {
  switch (tradingType) {
    case 'scalping':
      return TIMEFRAME_CONFIGS["15分钟"];
    case 'day':
      return TIMEFRAME_CONFIGS["4小时"];
    case 'swing':
      return TIMEFRAME_CONFIGS["12小时"];
    case 'position':
      return TIMEFRAME_CONFIGS["24小时"];
    default:
      return getRecommendedConfig("1小时");
  }
}

/**
 * 自定义配置验证
 */
export function validateConfig(config: RangeValidationConfig): { valid: boolean; errors: string[] } {
  const errors: string[] = [];
  
  if (config.samplingInterval <= 0) {
    errors.push("采样间隔必须大于0分钟");
  }
  
  if (config.samplingInterval > 60) {
    errors.push("采样间隔不应超过60分钟，否则失去范围验证的意义");
  }
  
  if (config.tolerancePercent < 0) {
    errors.push("价格容忍度不能为负数");
  }
  
  if (config.tolerancePercent > 5) {
    errors.push("价格容忍度过大(>5%)，可能影响验证准确性");
  }
  
  return {
    valid: errors.length === 0,
    errors
  };
}

/**
 * 获取所有可用的时间框架
 */
export function getAvailableTimeframes(): string[] {
  return Object.keys(TIMEFRAME_CONFIGS);
}

/**
 * 获取配置建议
 */
export function getConfigSuggestions(symbol: string, volatility: 'low' | 'medium' | 'high'): {
  timeframe: string;
  config: RangeValidationConfig;
  reason: string;
}[] {
  const suggestions = [];
  
  if (volatility === 'high') {
    suggestions.push({
      timeframe: "15分钟",
      config: TIMEFRAME_CONFIGS["15分钟"],
      reason: "高波动性适合短期快速交易"
    });
    
    suggestions.push({
      timeframe: "30分钟", 
      config: TIMEFRAME_CONFIGS["30分钟"],
      reason: "高波动性的稍长时间框架"
    });
  }
  
  if (volatility === 'medium') {
    suggestions.push({
      timeframe: "1小时",
      config: TIMEFRAME_CONFIGS["1小时"],
      reason: "中等波动性的标准配置"
    });
    
    suggestions.push({
      timeframe: "2小时",
      config: TIMEFRAME_CONFIGS["2小时"], 
      reason: "中等波动性的平衡选择"
    });
  }
  
  if (volatility === 'low') {
    suggestions.push({
      timeframe: "4小时",
      config: TIMEFRAME_CONFIGS["4小时"],
      reason: "低波动性需要更长时间框架"
    });
    
    suggestions.push({
      timeframe: "8小时",
      config: TIMEFRAME_CONFIGS["8小时"],
      reason: "低波动性的宽松配置"
    });
  }
  
  return suggestions;
}

export default {
  TIMEFRAME_CONFIGS,
  getRecommendedConfig,
  getConfigByTradingType,
  validateConfig,
  getAvailableTimeframes,
  getConfigSuggestions
};
