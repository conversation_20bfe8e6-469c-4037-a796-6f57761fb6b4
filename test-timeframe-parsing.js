/**
 * 测试时间框架解析逻辑
 */

function parseTimeframe(timeframe) {
  console.log(`解析时间框架: "${timeframe}"`);
  
  if (timeframe.includes("分钟")) {
    const minutes = parseInt(timeframe);
    const milliseconds = minutes * 60 * 1000;
    console.log(`  提取分钟数: ${minutes}`);
    console.log(`  转换为毫秒: ${milliseconds}`);
    console.log(`  等于: ${milliseconds / (60 * 1000)}分钟`);
    return milliseconds;
  }
  
  if (timeframe.includes("小时")) {
    const hours = parseInt(timeframe);
    const milliseconds = hours * 60 * 60 * 1000;
    console.log(`  提取小时数: ${hours}`);
    console.log(`  转换为毫秒: ${milliseconds}`);
    console.log(`  等于: ${milliseconds / (60 * 60 * 1000)}小时`);
    return milliseconds;
  }
  
  const defaultMs = 2 * 60 * 60 * 1000; // 默认2小时
  console.log(`  使用默认值: ${defaultMs}毫秒 (2小时)`);
  return defaultMs;
}

// 测试不同的时间框架
const testCases = [
  "15分钟-2小时",
  "30分钟",
  "1小时",
  "2小时-8小时",
  "8小时-24小时",
  "无效格式"
];

console.log("=== 时间框架解析测试 ===\n");

testCases.forEach((timeframe, index) => {
  console.log(`测试 ${index + 1}: ${timeframe}`);
  const result = parseTimeframe(timeframe);
  
  // 计算验证时间
  const now = new Date();
  const scheduledAt = new Date(now.getTime() + result);
  
  console.log(`  当前时间: ${now}`);
  console.log(`  验证时间: ${scheduledAt}`);
  console.log(`  时间差: ${Math.floor(result / (60 * 1000))}分钟\n`);
});

// 测试实际的数据库记录
console.log("=== 模拟实际场景 ===");
const actualTimeframe = "15分钟-2小时";
const actualResult = parseTimeframe(actualTimeframe);
const now = new Date();
const scheduledAt = new Date(now.getTime() + actualResult);

console.log(`实际时间框架: ${actualTimeframe}`);
console.log(`解析结果: ${actualResult}毫秒`);
console.log(`当前时间: ${now}`);
console.log(`计划验证时间: ${scheduledAt}`);
console.log(`需要等待: ${Math.floor(actualResult / (60 * 1000))}分钟`);
