import { dataService } from "./data-service";
import { aiAnalysisService } from "./ai-analysis";
import { strategyService } from "./trading-strategy";
import { FeishuWebhookService, TradingAdviceMessage } from "./feishu-webhook";
import { schedulerConfigService } from "./scheduler-config";

/**
 * 分析结果接口
 */
export interface AnalysisResult {
  symbol: string;
  success: boolean;
  data?: any;
  error?: string;
  timestamp: string;
}

/**
 * 定时分析服务
 * 只提供执行分析的核心功能，不包含定时任务管理
 */
export class TradingSchedulerService {
  private static instance: TradingSchedulerService;
  private feishuService: FeishuWebhookService | null = null;

  private constructor() {
    this.initializeFeishuService();
  }

  /**
   * 获取单例实例
   */
  static getInstance(): TradingSchedulerService {
    if (!TradingSchedulerService.instance) {
      TradingSchedulerService.instance = new TradingSchedulerService();
    }
    return TradingSchedulerService.instance;
  }

  /**
   * 初始化飞书服务
   */
  private initializeFeishuService(): void {
    const config = schedulerConfigService.getConfig();
    if (config.feishu.enabled && config.feishu.webhookUrl) {
      this.feishuService = new FeishuWebhookService(config.feishu.webhookUrl);
    }
  }

  /**
   * 手动执行分析
   */
  async executeManualAnalysis(symbols?: string[]): Promise<AnalysisResult[]> {
    try {
      const config = schedulerConfigService.getConfig();
      const analysisSymbols = symbols || config.analysis.symbols;

      console.log(`开始手动分析: ${analysisSymbols.join(", ")}`);

      // 重新初始化飞书服务（确保使用最新配置）
      this.initializeFeishuService();
      console.log(
        `飞书服务初始化状态: ${this.feishuService ? "已初始化" : "未初始化"}`
      );

      if (this.feishuService) {
        console.log(
          `飞书配置: enabled=${config.feishu.enabled}, webhookUrl=${
            config.feishu.webhookUrl ? "已配置" : "未配置"
          }`
        );
      }

      const results: AnalysisResult[] = [];

      for (const symbol of analysisSymbols) {
        const result = await this.analyzeSymbol(symbol);
        results.push(result);
      }

      console.log(
        `分析结果: 成功=${results.filter((r) => r.success).length}, 失败=${
          results.filter((r) => !r.success).length
        }`
      );

      // 发送结果到飞书
      console.log("开始发送结果到飞书...");
      await this.sendResultsToFeishu(results);
      console.log("飞书消息发送完成");

      console.log(
        `手动分析完成，成功: ${results.filter((r) => r.success).length}/${
          results.length
        }`
      );

      return results;
    } catch (error) {
      console.error("执行手动分析失败:", error);

      // 发送错误消息到飞书
      if (this.feishuService) {
        await this.feishuService.sendErrorMessage(
          error instanceof Error ? error.message : "未知错误"
        );
      }

      throw error;
    }
  }

  /**
   * 分析单个币种
   */
  private async analyzeSymbol(symbol: string): Promise<AnalysisResult> {
    const timestamp = new Date().toLocaleString("zh-CN");

    try {
      console.log(`开始分析 ${symbol}...`);

      const config = schedulerConfigService.getConfig();

      // 1. 获取市场数据
      const marketData = await dataService.getMarketData(symbol);
      console.log(`获取到 ${symbol} 的市场数据`);

      // 2. 计算技术指标
      const technicalIndicators = dataService.calculateTechnicalIndicators(
        marketData.oneMin
      );
      console.log(`计算 ${symbol} 技术指标完成`);

      // 3. 执行AI分析
      const analysisResult = await aiAnalysisService.analyzeMarketWithConfig(
        marketData,
        technicalIndicators,
        config.openai,
        config.analysis.riskTolerance
      );
      console.log(`${symbol} AI分析完成`);

      // 4. 生成交易建议
      const tradingAdvice = strategyService.generateTradingAdvice(
        analysisResult,
        10000,
        config.analysis.riskTolerance
      );

      const response = {
        analysis: analysisResult,
        advice: tradingAdvice,
        marketData: {
          symbol: marketData.symbol,
          latestPrice:
            marketData.oneMin[marketData.oneMin.length - 1]?.close || 0,
          dailyChange:
            marketData.daily[marketData.daily.length - 1]?.changePercent || 0,
          volume24h: marketData.daily[marketData.daily.length - 1]?.volume || 0,
        },
      };

      return {
        symbol,
        success: true,
        data: response,
        timestamp,
      };
    } catch (error) {
      console.error(`分析 ${symbol} 失败:`, error);
      return {
        symbol,
        success: false,
        error: error instanceof Error ? error.message : "未知错误",
        timestamp,
      };
    }
  }

  /**
   * 发送结果到飞书
   */
  private async sendResultsToFeishu(results: AnalysisResult[]): Promise<void> {
    console.log(
      `sendResultsToFeishu: 飞书服务状态=${
        this.feishuService ? "可用" : "不可用"
      }`
    );

    if (!this.feishuService) {
      console.log("飞书服务未初始化，跳过消息发送");
      return;
    }

    const config = schedulerConfigService.getConfig();
    console.log(
      `通知配置: onlySignals=${config.notification.onlySignals}, errorNotification=${config.notification.errorNotification}`
    );

    for (const result of results) {
      try {
        console.log(
          `处理 ${result.symbol} 的分析结果: success=${result.success}`
        );

        if (result.success && result.data) {
          const { advice, marketData } = result.data;
          console.log(`${result.symbol} 交易建议: ${advice.action}`);

          // 检查是否只发送交易信号
          if (config.notification.onlySignals && advice.action === "HOLD") {
            console.log(`${result.symbol}: 跳过HOLD信号（仅发送交易信号模式）`);
            continue;
          }

          const message: TradingAdviceMessage = {
            symbol: result.symbol,
            action: advice.action,
            entryPrice: advice.entryPrice,
            quantity: advice.quantity,
            stopLoss: advice.stopLoss,
            takeProfit: advice.takeProfit,
            timeframe: advice.timeframe,
            confidence: advice.confidence,
            reasoning: advice.reasoning,
            riskLevel: advice.riskLevel,
            timestamp: result.timestamp,
            marketData,
            positionManagement: result.data.positionManagement,
            rollingStrategy: result.data.rollingStrategy,
            riskReward: result.data.riskReward,
            multiTimeframePrediction:
              result.data.analysis?.multiTimeframePrediction,
          };

          console.log(`正在发送 ${result.symbol} 的交易建议到飞书...`);
          const sendResult = await this.feishuService.sendTradingAdvice(
            message,
            config.notification.atAll
          );
          console.log(
            `${result.symbol} 飞书消息发送结果: ${sendResult ? "成功" : "失败"}`
          );
        } else if (result.error && config.notification.errorNotification) {
          console.log(`正在发送 ${result.symbol} 的错误消息到飞书...`);
          const sendResult = await this.feishuService.sendErrorMessage(
            result.error,
            result.symbol
          );
          console.log(
            `${result.symbol} 错误消息发送结果: ${sendResult ? "成功" : "失败"}`
          );
        }
      } catch (error) {
        console.error(`发送 ${result.symbol} 结果到飞书失败:`, error);
      }
    }
  }

  /**
   * 测试飞书连接
   */
  async testFeishuConnection(): Promise<{ success: boolean; message: string }> {
    try {
      this.initializeFeishuService();

      if (!this.feishuService) {
        return {
          success: false,
          message: "飞书服务未配置",
        };
      }

      const success = await this.feishuService.testConnection();

      return {
        success,
        message: success ? "飞书连接测试成功" : "飞书连接测试失败",
      };
    } catch (error) {
      return {
        success: false,
        message: `测试失败: ${
          error instanceof Error ? error.message : "未知错误"
        }`,
      };
    }
  }
}

/**
 * 获取定时分析服务实例
 */
export const tradingScheduler = TradingSchedulerService.getInstance();
