import { ProcessedKlineData, MarketData } from "@/types/trading";

export interface MarketSentimentResult {
  fearGreedIndex: number; // 0-100
  marketPhase:
    | "ACCUMULATION"
    | "MARKUP"
    | "DISTRIBUTION"
    | "MARKDOWN"
    | "CONSOLIDATION";
  volumeProfile: "BULLISH" | "BEARISH" | "NEUTRAL";
  priceAction: "STRONG_TREND" | "WEAK_TREND" | "CONSOLIDATION" | "REVERSAL";
  smartMoneyFlow: "INFLOW" | "OUTFLOW" | "NEUTRAL";
  retailSentiment:
    | "EXTREME_FEAR"
    | "FEAR"
    | "NEUTRAL"
    | "GREED"
    | "EXTREME_GREED";
  confidence: number;
  signals: string[];
}

export class MarketSentimentAnalyzer {
  private static instance: MarketSentimentAnalyzer;

  static getInstance(): MarketSentimentAnalyzer {
    if (!MarketSentimentAnalyzer.instance) {
      MarketSentimentAnalyzer.instance = new MarketSentimentAnalyzer();
    }
    return MarketSentimentAnalyzer.instance;
  }

  /**
   * 分析市场情绪
   */
  analyzeMarketSentiment(marketData: MarketData): MarketSentimentResult {
    const { daily, hourly, oneMin } = marketData;

    // 计算恐慌贪婪指数
    const fearGreedIndex = this.calculateFearGreedIndex(daily);

    // 判断市场阶段
    const marketPhase = this.identifyMarketPhase(daily);

    // 分析成交量特征
    const volumeProfile = this.analyzeVolumeProfile(daily);

    // 分析价格行为
    const priceAction = this.analyzePriceAction(hourly);

    // 分析聪明钱流向
    const smartMoneyFlow = this.analyzeSmartMoneyFlow(daily);

    // 分析散户情绪
    const retailSentiment = this.analyzeRetailSentiment(oneMin);

    // 计算综合信心度
    const confidence = this.calculateConfidence(
      fearGreedIndex,
      marketPhase,
      volumeProfile
    );

    // 生成交易信号
    const signals = this.generateSentimentSignals(
      fearGreedIndex,
      marketPhase,
      volumeProfile,
      priceAction
    );

    return {
      fearGreedIndex,
      marketPhase,
      volumeProfile,
      priceAction,
      smartMoneyFlow,
      retailSentiment,
      confidence,
      signals,
    };
  }

  /**
   * 计算恐慌贪婪指数
   */
  private calculateFearGreedIndex(data: ProcessedKlineData[]): number {
    if (data.length < 14) return 50;

    const recent = data.slice(-14);
    let score = 0;

    // 价格动量 (25%)
    const priceChange =
      (recent[recent.length - 1].close - recent[0].close) / recent[0].close;
    const momentumScore = Math.max(0, Math.min(100, 50 + priceChange * 500));
    score += momentumScore * 0.25;

    // 波动率 (25%)
    const volatility = this.calculateVolatility(recent);
    const volatilityScore = Math.max(0, Math.min(100, 100 - volatility * 2));
    score += volatilityScore * 0.25;

    // 成交量 (25%)
    const avgVolume =
      recent.slice(0, -7).reduce((sum, d) => sum + d.volume, 0) / 7;
    const recentVolume =
      recent.slice(-7).reduce((sum, d) => sum + d.volume, 0) / 7;
    const volumeRatio = recentVolume / avgVolume;
    const volumeScore = Math.max(0, Math.min(100, volumeRatio * 50));
    score += volumeScore * 0.25;

    // 市场广度 (25%)
    const upDays = recent.filter((d) => d.changePercent > 0).length;
    const breadthScore = (upDays / recent.length) * 100;
    score += breadthScore * 0.25;

    return Math.round(score);
  }

  /**
   * 识别市场阶段
   */
  private identifyMarketPhase(
    data: ProcessedKlineData[]
  ): MarketSentimentResult["marketPhase"] {
    if (data.length < 30) return "CONSOLIDATION";

    const recent = data.slice(-30);
    const priceChange =
      (recent[recent.length - 1].close - recent[0].close) / recent[0].close;
    const avgVolume =
      recent.reduce((sum, d) => sum + d.volume, 0) / recent.length;
    const recentVolume =
      recent.slice(-7).reduce((sum, d) => sum + d.volume, 0) / 7;
    const volumeIncrease = recentVolume / avgVolume;

    // 累积阶段：价格横盘，成交量逐渐增加
    if (Math.abs(priceChange) < 0.05 && volumeIncrease > 1.2) {
      return "ACCUMULATION";
    }

    // 上涨阶段：价格上涨，成交量放大
    if (priceChange > 0.1 && volumeIncrease > 1.1) {
      return "MARKUP";
    }

    // 派发阶段：价格高位震荡，成交量放大
    if (Math.abs(priceChange) < 0.1 && volumeIncrease > 1.3) {
      const highPrice = Math.max(...recent.map((d) => d.high));
      const currentPrice = recent[recent.length - 1].close;
      if (currentPrice / highPrice > 0.9) {
        return "DISTRIBUTION";
      }
    }

    // 下跌阶段：价格下跌
    if (priceChange < -0.1) {
      return "MARKDOWN";
    }

    return "CONSOLIDATION";
  }

  /**
   * 分析成交量特征
   */
  private analyzeVolumeProfile(
    data: ProcessedKlineData[]
  ): MarketSentimentResult["volumeProfile"] {
    if (data.length < 14) return "NEUTRAL";

    const recent = data.slice(-14);
    let bullishVolume = 0;
    let bearishVolume = 0;

    recent.forEach((d) => {
      if (d.changePercent > 0) {
        bullishVolume += d.volume;
      } else {
        bearishVolume += d.volume;
      }
    });

    const ratio = bullishVolume / (bullishVolume + bearishVolume);

    if (ratio > 0.6) return "BULLISH";
    if (ratio < 0.4) return "BEARISH";
    return "NEUTRAL";
  }

  /**
   * 分析价格行为
   */
  private analyzePriceAction(
    data: ProcessedKlineData[]
  ): MarketSentimentResult["priceAction"] {
    if (data.length < 24) return "CONSOLIDATION";

    const recent = data.slice(-24);
    const priceChange =
      (recent[recent.length - 1].close - recent[0].close) / recent[0].close;
    const volatility = this.calculateVolatility(recent);

    // 强趋势：价格变化大且波动率适中
    if (Math.abs(priceChange) > 0.05 && volatility < 0.3) {
      return "STRONG_TREND";
    }

    // 弱趋势：价格变化中等
    if (Math.abs(priceChange) > 0.02 && Math.abs(priceChange) <= 0.05) {
      return "WEAK_TREND";
    }

    // 反转：价格在近期出现明显转向
    const midPoint = Math.floor(recent.length / 2);
    const firstHalf = recent.slice(0, midPoint);
    const secondHalf = recent.slice(midPoint);

    const firstHalfChange =
      (firstHalf[firstHalf.length - 1].close - firstHalf[0].close) /
      firstHalf[0].close;
    const secondHalfChange =
      (secondHalf[secondHalf.length - 1].close - secondHalf[0].close) /
      secondHalf[0].close;

    if (
      firstHalfChange * secondHalfChange < 0 &&
      Math.abs(firstHalfChange) > 0.02
    ) {
      return "REVERSAL";
    }

    return "CONSOLIDATION";
  }

  /**
   * 分析聪明钱流向
   */
  private analyzeSmartMoneyFlow(
    data: ProcessedKlineData[]
  ): MarketSentimentResult["smartMoneyFlow"] {
    if (data.length < 7) return "NEUTRAL";

    const recent = data.slice(-7);
    let smartMoneyScore = 0;

    recent.forEach((d) => {
      // 大阳线或大阴线通常代表机构行为
      if (Math.abs(d.changePercent) > 0.03) {
        if (d.changePercent > 0) {
          smartMoneyScore += d.volume;
        } else {
          smartMoneyScore -= d.volume;
        }
      }
    });

    const totalVolume = recent.reduce((sum, d) => sum + d.volume, 0);
    const ratio = smartMoneyScore / totalVolume;

    if (ratio > 0.1) return "INFLOW";
    if (ratio < -0.1) return "OUTFLOW";
    return "NEUTRAL";
  }

  /**
   * 分析散户情绪
   */
  private analyzeRetailSentiment(
    data: ProcessedKlineData[]
  ): MarketSentimentResult["retailSentiment"] {
    if (data.length < 60) return "NEUTRAL";

    const recent = data.slice(-60);
    const priceChange =
      (recent[recent.length - 1].close - recent[0].close) / recent[0].close;
    const volatility = this.calculateVolatility(recent);

    // 基于价格变化和波动率判断散户情绪
    if (priceChange < -0.05 && volatility > 0.4) return "EXTREME_FEAR";
    if (priceChange < -0.02 && volatility > 0.2) return "FEAR";
    if (priceChange > 0.05 && volatility > 0.4) return "EXTREME_GREED";
    if (priceChange > 0.02 && volatility > 0.2) return "GREED";

    return "NEUTRAL";
  }

  /**
   * 计算综合信心度
   */
  private calculateConfidence(
    fearGreedIndex: number,
    marketPhase: MarketSentimentResult["marketPhase"],
    volumeProfile: MarketSentimentResult["volumeProfile"]
  ): number {
    let confidence = 50;

    // 恐慌贪婪指数影响
    if (fearGreedIndex < 20 || fearGreedIndex > 80) {
      confidence += 20; // 极端情绪时信心度高
    }

    // 市场阶段影响
    if (marketPhase === "ACCUMULATION" || marketPhase === "MARKUP") {
      confidence += 15;
    }

    // 成交量确认
    if (volumeProfile !== "NEUTRAL") {
      confidence += 10;
    }

    return Math.min(100, confidence);
  }

  /**
   * 生成情绪信号
   */
  private generateSentimentSignals(
    fearGreedIndex: number,
    marketPhase: MarketSentimentResult["marketPhase"],
    volumeProfile: MarketSentimentResult["volumeProfile"],
    priceAction: MarketSentimentResult["priceAction"]
  ): string[] {
    const signals: string[] = [];

    if (fearGreedIndex < 25) {
      signals.push("极度恐慌，考虑逢低买入机会");
    } else if (fearGreedIndex > 75) {
      signals.push("极度贪婪，注意风险控制");
    }

    if (marketPhase === "ACCUMULATION") {
      signals.push("处于累积阶段，适合建仓");
    } else if (marketPhase === "DISTRIBUTION") {
      signals.push("处于派发阶段，考虑减仓");
    }

    if (volumeProfile === "BULLISH" && priceAction === "STRONG_TREND") {
      signals.push("成交量确认上涨趋势，可考虑加仓");
    } else if (volumeProfile === "BEARISH" && priceAction === "STRONG_TREND") {
      signals.push("成交量确认下跌趋势，建议减仓");
    }

    return signals;
  }

  /**
   * 计算波动率
   */
  private calculateVolatility(data: ProcessedKlineData[]): number {
    if (data.length < 2) return 0;

    const returns = data
      .slice(1)
      .map((item, index) => Math.log(item.close / data[index].close));

    const mean = returns.reduce((sum, r) => sum + r, 0) / returns.length;
    const variance =
      returns.reduce((sum, r) => sum + Math.pow(r - mean, 2), 0) /
      returns.length;

    return Math.sqrt(variance) * Math.sqrt(365); // 年化波动率
  }
}

export const marketSentimentAnalyzer = MarketSentimentAnalyzer.getInstance();
