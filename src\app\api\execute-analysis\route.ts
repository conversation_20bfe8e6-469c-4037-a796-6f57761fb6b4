import { NextRequest, NextResponse } from "next/server";
import { dataService } from "@/lib/data-service";
import { aiAnalysisService } from "@/lib/ai-analysis";
import { strategyService } from "@/lib/trading-strategy";
import {
  FeishuWebhookService,
  TradingAdviceMessage,
} from "@/lib/feishu-webhook";
import { schedulerConfigService } from "@/lib/scheduler-config";

/**
 * 执行定时分析的API接口
 * 用于被外部cron脚本调用，执行BTC和ETH的激进型分析
 */
export async function POST(request: NextRequest) {
  try {
    console.log("收到定时分析执行请求...");

    // 获取配置
    const config = schedulerConfigService.getConfig();

    // 验证配置
    const validation = schedulerConfigService.validateConfig();
    if (!validation.valid) {
      return NextResponse.json(
        {
          success: false,
          error: `配置验证失败: ${validation.errors.join(", ")}`,
        },
        { status: 400 }
      );
    }

    // 检查是否启用了定时分析
    if (!config.analysis.enabled) {
      return NextResponse.json(
        {
          success: false,
          error: "定时分析未启用",
        },
        { status: 400 }
      );
    }

    // 初始化飞书服务
    let feishuService: FeishuWebhookService | null = null;
    if (config.feishu.enabled && config.feishu.webhookUrl) {
      feishuService = new FeishuWebhookService(config.feishu.webhookUrl);
    }

    // 执行分析的币种（固定为BTC和ETH的激进型分析）
    const symbols = ["BTCUSDT", "ETHUSDT"];
    const riskTolerance = "HIGH"; // 激进型分析

    // 创建单个币种分析的异步函数
    const analyzeSymbol = async (symbol: string) => {
      try {
        console.log(`开始分析 ${symbol}...`);

        // 1. 获取市场数据
        const marketData = await dataService.getMarketData(symbol);
        console.log(`获取到 ${symbol} 的市场数据`);

        // 2. 计算技术指标
        const technicalIndicators = dataService.calculateTechnicalIndicators(
          marketData.oneMin
        );
        console.log(`计算 ${symbol} 技术指标完成`);

        // 3. 执行AI分析
        const analysisResult = await aiAnalysisService.analyzeMarketWithConfig(
          marketData,
          technicalIndicators,
          config.openai,
          riskTolerance
        );
        console.log(`${symbol} AI分析完成`);

        // 4. 生成交易建议
        const tradingAdvice = strategyService.generateTradingAdvice(
          analysisResult,
          10000, // 默认账户余额
          riskTolerance
        );

        // 5. 生成滚仓策略
        const rollingStrategy = await aiAnalysisService.generateRollingStrategy(
          marketData,
          analysisResult
        );

        // 6. 生成仓位管理策略
        const positionManagement = strategyService.generatePositionManagement(
          analysisResult,
          rollingStrategy
        );

        // 7. 计算风险收益比
        const riskReward = strategyService.calculateRiskReward(
          tradingAdvice.entryPrice,
          tradingAdvice.stopLoss,
          tradingAdvice.takeProfit
        );

        // 8. 趋势分析
        const trendAnalysis = strategyService.analyzeTrend(marketData.daily);

        const analysisData = {
          symbol,
          analysis: analysisResult,
          advice: tradingAdvice,
          positionManagement,
          rollingStrategy,
          riskReward,
          trendAnalysis,
          marketData: {
            symbol: marketData.symbol,
            latestPrice:
              marketData.oneMin[marketData.oneMin.length - 1]?.close || 0,
            dailyChange:
              marketData.daily[marketData.daily.length - 1]?.changePercent || 0,
            volume24h:
              marketData.daily[marketData.daily.length - 1]?.volume || 0,
          },
          timestamp: new Date().toISOString(),
        };

        // 发送到飞书
        if (feishuService) {
          try {
            const message: TradingAdviceMessage = {
              symbol,
              action: tradingAdvice.action,
              entryPrice: tradingAdvice.entryPrice,
              quantity: tradingAdvice.quantity,
              stopLoss: tradingAdvice.stopLoss,
              takeProfit: tradingAdvice.takeProfit,
              timeframe: tradingAdvice.timeframe,
              confidence: tradingAdvice.confidence,
              reasoning: tradingAdvice.reasoning,
              riskLevel: tradingAdvice.riskLevel,
              timestamp: new Date().toLocaleString("zh-CN"),
              marketData: {
                latestPrice:
                  marketData.oneMin[marketData.oneMin.length - 1]?.close || 0,
                dailyChange:
                  marketData.daily[marketData.daily.length - 1]
                    ?.changePercent || 0,
                volume24h:
                  marketData.daily[marketData.daily.length - 1]?.volume || 0,
              },
              positionManagement,
              rollingStrategy,
              riskReward,
              multiTimeframePrediction: analysisResult.multiTimeframePrediction,
            };

            await feishuService.sendTradingAdvice(
              message,
              config.notification.atAll
            );
            console.log(`${symbol} 分析结果已发送到飞书`);
          } catch (feishuError) {
            console.error(`发送 ${symbol} 分析结果到飞书失败:`, feishuError);
          }
        }

        console.log(`${symbol} 分析完成`);

        return {
          symbol,
          success: true,
          data: analysisData,
        };
      } catch (error) {
        console.error(`分析 ${symbol} 失败:`, error);
        return {
          symbol,
          success: false,
          error: error instanceof Error ? error.message : "未知错误",
        };
      }
    };

    // 使用 Promise.allSettled 并发执行所有币种的分析
    console.log("开始并发执行所有币种分析...");
    const analysisPromises = symbols.map((symbol) => analyzeSymbol(symbol));
    const settledResults = await Promise.allSettled(analysisPromises);

    // 处理结果
    const results = settledResults.map((result, index) => {
      if (result.status === "fulfilled") {
        return result.value;
      } else {
        console.error(
          `币种 ${symbols[index]} 分析Promise被拒绝:`,
          result.reason
        );
        return {
          symbol: symbols[index],
          success: false,
          error:
            result.reason instanceof Error
              ? result.reason.message
              : "Promise被拒绝",
        };
      }
    });

    console.log("所有币种分析完成");

    const successCount = results.filter((r) => r.success).length;
    const totalCount = results.length;

    return NextResponse.json({
      success: successCount > 0,
      message: `定时分析执行完成，成功: ${successCount}/${totalCount}`,
      results,
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    console.error("定时分析执行失败:", error);
    return NextResponse.json(
      {
        success: false,
        error: error instanceof Error ? error.message : "未知错误",
        timestamp: new Date().toISOString(),
      },
      { status: 500 }
    );
  }
}

/**
 * 获取定时分析状态
 */
export async function GET() {
  try {
    const config = schedulerConfigService.getConfig();

    return NextResponse.json({
      success: true,
      data: {
        analysisEnabled: config.analysis.enabled,
        symbols: ["BTCUSDT", "ETHUSDT"],
        riskTolerance: "HIGH",
        feishuEnabled: config.feishu.enabled,
        openaiConfigured: !!config.openai.apiKey,
        feishuConfigured: config.feishu.enabled && !!config.feishu.webhookUrl,
      },
    });
  } catch (error) {
    console.error("获取定时分析状态失败:", error);
    return NextResponse.json(
      {
        success: false,
        error: error instanceof Error ? error.message : "未知错误",
      },
      { status: 500 }
    );
  }
}
