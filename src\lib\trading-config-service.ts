import { TradingAPIConfig, TradingConfigTestResult } from "@/types/trading";
import crypto from "crypto";

export class TradingConfigService {
  private static instance: TradingConfigService;
  private readonly STORAGE_KEY = "ai-trading-api-config";

  // 默认配置
  private readonly DEFAULT_CONFIG: TradingAPIConfig = {
    baseURL: "https://fapi.binance.com",
    apiKey: "",
    secretKey: "",
    testnet: false,
    name: "主网配置",
  };

  // 常用交易所配置
  private readonly COMMON_EXCHANGES = [
    {
      name: "币安期货主网",
      baseURL: "https://fapi.binance.com",
      testnet: false,
    },
    {
      name: "币安期货测试网",
      baseURL: "https://testnet.binancefuture.com",
      testnet: true,
    },
  ];

  static getInstance(): TradingConfigService {
    if (!TradingConfigService.instance) {
      TradingConfigService.instance = new TradingConfigService();
    }
    return TradingConfigService.instance;
  }

  /**
   * 获取当前配置
   * 优先级：本地存储 > 默认配置
   */
  getConfig(): TradingAPIConfig {
    // 尝试从本地存储获取
    if (typeof window !== "undefined") {
      try {
        const stored = localStorage.getItem(this.STORAGE_KEY);
        if (stored) {
          const config = JSON.parse(stored);
          // 验证配置完整性
          if (this.isValidConfig(config)) {
            return config;
          }
        }
      } catch (error) {
        console.warn("读取交易API配置失败:", error);
      }
    }

    return this.DEFAULT_CONFIG;
  }

  /**
   * 保存配置到本地存储
   */
  saveConfig(config: TradingAPIConfig): boolean {
    if (!this.isValidConfig(config)) {
      return false;
    }

    try {
      if (typeof window !== "undefined") {
        localStorage.setItem(this.STORAGE_KEY, JSON.stringify(config));
        return true;
      }
    } catch (error) {
      console.error("保存交易API配置失败:", error);
    }

    return false;
  }

  /**
   * 清除配置
   */
  clearConfig(): void {
    try {
      if (typeof window !== "undefined") {
        localStorage.removeItem(this.STORAGE_KEY);
      }
    } catch (error) {
      console.error("清除交易API配置失败:", error);
    }
  }

  /**
   * 验证配置格式
   */
  private isValidConfig(config: any): config is TradingAPIConfig {
    return (
      config &&
      typeof config === "object" &&
      typeof config.baseURL === "string" &&
      typeof config.apiKey === "string" &&
      typeof config.secretKey === "string" &&
      typeof config.testnet === "boolean"
    );
  }

  /**
   * 检查配置是否完整
   */
  isConfigured(): boolean {
    const config = this.getConfig();
    return !!(config.apiKey && config.secretKey && config.baseURL);
  }

  /**
   * 测试配置是否可用
   */
  async testConfig(config: TradingAPIConfig): Promise<TradingConfigTestResult> {
    if (!this.isValidConfig(config)) {
      return {
        success: false,
        message: "配置格式无效",
      };
    }

    if (!config.apiKey.trim()) {
      return {
        success: false,
        message: "API Key 不能为空",
      };
    }

    if (!config.secretKey.trim()) {
      return {
        success: false,
        message: "Secret Key 不能为空",
      };
    }

    try {
      const startTime = Date.now();

      const response = await fetch("/api/trading/test", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(config),
      });

      const result = await response.json();
      const latency = Date.now() - startTime;

      if (result.success) {
        return {
          success: true,
          message: "交易API配置测试成功",
          latency,
          accountInfo: result.accountInfo,
        };
      } else {
        return {
          success: false,
          message: result.error || "交易API配置测试失败",
        };
      }
    } catch (error) {
      return {
        success: false,
        message: "网络错误: " + error,
      };
    }
  }

  /**
   * 生成API签名
   */
  generateSignature(
    queryString: string,
    secretKey: string,
    timestamp?: number
  ): string {
    const ts = timestamp || Date.now();
    const signaturePayload = `${queryString}&timestamp=${ts}`;

    return crypto
      .createHmac("sha256", secretKey)
      .update(signaturePayload)
      .digest("hex");
  }

  /**
   * 创建带签名的请求头
   */
  createAuthHeaders(
    apiKey: string,
    secretKey: string,
    queryString: string = "",
    timestamp?: number
  ): Record<string, string> {
    const ts = timestamp || Date.now();
    const signature = this.generateSignature(queryString, secretKey, ts);

    return {
      "X-MBX-APIKEY": apiKey,
      "Content-Type": "application/json",
    };
  }

  /**
   * 获取常用交易所配置列表
   */
  getCommonExchanges() {
    return this.COMMON_EXCHANGES;
  }

  /**
   * 获取配置状态信息
   */
  getConfigStatus(): {
    configured: boolean;
    configName?: string;
    testnet?: boolean;
    baseURL?: string;
  } {
    const config = this.getConfig();
    const configured = this.isConfigured();

    return {
      configured,
      configName: configured ? config.name : undefined,
      testnet: configured ? config.testnet : undefined,
      baseURL: configured ? config.baseURL : undefined,
    };
  }
}

export const tradingConfigService = TradingConfigService.getInstance();
