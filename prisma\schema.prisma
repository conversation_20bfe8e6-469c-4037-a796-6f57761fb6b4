// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "sqlite"
  url      = env("DATABASE_URL")
}

// 预测记录表
model PredictionRecord {
  id        String   @id @default(cuid())
  symbol    String   // 交易对符号
  timestamp DateTime @default(now()) // 预测时间

  // 预测信息
  direction     String  // BUY/SELL/HOLD
  entryPrice    Float   // 入场价格
  targetPrice   Float   // 目标价格
  stopLoss      Float   // 止损价格
  timeframe     String  // 时间框架
  confidence    Int     // 信心度 0-100
  reasoning     String  // 预测理由

  // 实际结果（验证后填入）
  actualPrice          Float?    // 实际价格
  actualDirection      String?   // 实际方向 UP/DOWN/SIDEWAYS
  priceChange          Float?    // 价格变化
  priceChangePercent   Float?    // 价格变化百分比
  hitTarget            Boolean?  // 是否达到目标
  partialHitTarget     Boolean?  // 是否部分达到目标（新增）
  hitStopLoss          Boolean?  // 是否触及止损
  verifiedAt           DateTime? // 验证时间

  // 准确性评估
  directionCorrect     Boolean?  // 方向是否正确
  priceAccuracy        Float?    // 价格准确度 0-100
  overallScore         Float?    // 综合评分 0-100

  // 关联的分析结果
  analysisResult       AnalysisResult? @relation(fields: [analysisResultId], references: [id])
  analysisResultId     String?

  @@map("prediction_records")
}

// 分析结果表
model AnalysisResult {
  id        String   @id @default(cuid())
  symbol    String   // 交易对符号
  timestamp DateTime @default(now()) // 分析时间

  // 市场趋势
  shortTermTrend  String // BULLISH/BEARISH/NEUTRAL
  mediumTermTrend String
  longTermTrend   String

  // 交易信号
  signalDirection String  // LONG/SHORT/HOLD
  signalConfidence Int    // 信心度
  signalEntryPrice Float  // 入场价格
  signalStopLoss   Float  // 止损价格
  signalTakeProfit String // 止盈价格（JSON数组）
  signalReasoning  String // 信号理由

  // 风险管理
  maxDrawdown      Float  // 最大回撤
  riskReward       Float  // 风险收益比
  winRate          Float  // 胜率
  expectedReturn   Float  // 期望收益

  // 市场条件
  volatility String // HIGH/MEDIUM/LOW
  volume     String // HIGH/MEDIUM/LOW
  momentum   String // STRONG/WEAK/NEUTRAL

  // 技术指标（JSON存储）
  technicalIndicators String // JSON格式的技术指标

  // AI洞察
  aiInsights String // AI分析洞察
  warnings   String // 警告信息（JSON数组）

  // 数据质量
  dataQualityScore Float?  // 数据质量评分
  dataQualityIssues String? // 数据质量问题（JSON数组）

  // 关联的预测记录
  predictions PredictionRecord[]

  @@map("analysis_results")
}

// 准确率统计表（定期汇总）
model AccuracyStats {
  id        String   @id @default(cuid())
  symbol    String?  // 币种（null表示全局统计）
  timeframe String?  // 时间框架（null表示全局统计）
  date      DateTime @default(now()) // 统计日期

  // 统计数据
  totalPredictions     Int   // 总预测数
  verifiedPredictions  Int   // 已验证预测数
  directionAccuracy    Float // 方向准确率
  priceAccuracy        Float // 价格准确率
  overallAccuracy      Float // 综合准确率

  // 按信心度统计
  highConfidenceCount    Int   @default(0) // 高信心预测数量
  highConfidenceAccuracy Float @default(0) // 高信心准确率
  mediumConfidenceCount    Int   @default(0)
  mediumConfidenceAccuracy Float @default(0)
  lowConfidenceCount    Int   @default(0)
  lowConfidenceAccuracy Float @default(0)

  // 趋势分析
  recentTrend Float @default(0) // 最近趋势变化

  @@unique([symbol, timeframe, date])
  @@map("accuracy_stats")
}

// 数据质量记录表
model DataQualityRecord {
  id        String   @id @default(cuid())
  symbol    String   // 交易对符号
  timestamp DateTime @default(now()) // 检查时间

  // 质量评估
  isValid Boolean // 数据是否有效
  score   Float   // 质量评分 0-100

  // 问题和警告（JSON格式）
  issues         String // 质量问题
  warnings       String // 警告信息
  recommendations String // 改进建议

  // 具体检查项
  dataCompleteness Boolean @default(true)  // 数据完整性
  dataConsistency  Boolean @default(true)  // 数据一致性
  dataFreshness    Boolean @default(true)  // 数据时效性
  hasOutliers      Boolean @default(false) // 是否有异常值
  volumeAnomalies  Boolean @default(false) // 成交量异常

  @@map("data_quality_records")
}

// 系统配置表
model SystemConfig {
  id    String @id @default(cuid())
  key   String @unique // 配置键
  value String // 配置值（JSON格式）

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@map("system_configs")
}

// 预测验证任务表
model ValidationTask {
  id           String   @id @default(cuid())
  predictionId String   // 预测记录ID
  scheduledAt  DateTime // 计划验证时间
  executedAt   DateTime? // 实际执行时间
  status       String   @default("PENDING") // PENDING/COMPLETED/FAILED
  errorMessage String?  // 错误信息

  // 验证时间区间字段
  validationStartTime DateTime? // 验证开始时间
  validationEndTime   DateTime? // 验证结束时间
  timeframe          String?   // 时间框架
  samplingInterval   Int?      // 采样间隔（分钟）
  tolerancePercent   Float?    // 价格容忍度百分比

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@map("validation_tasks")
}
