import {
  AIAnalysisResult,
  TradingAdvice,
  PositionManagement,
  RollingStrategy,
  TrendAnalysis,
  ProcessedKlineData,
} from "@/types/trading";
import {
  add,
  subtract,
  multiply,
  divide,
  abs,
  max,
  min,
  toBig,
  toNumber,
  gte,
  lte,
  gt,
  lt,
  formatNumber,
} from "@/lib/big-utils";

export class TradingStrategyService {
  private static instance: TradingStrategyService;

  static getInstance(): TradingStrategyService {
    if (!TradingStrategyService.instance) {
      TradingStrategyService.instance = new TradingStrategyService();
    }
    return TradingStrategyService.instance;
  }

  /**
   * 生成交易建议
   */
  generateTradingAdvice(
    analysisResult: AIAnalysisResult,
    accountBalance: number = 10000,
    riskTolerance: "LOW" | "MEDIUM" | "HIGH" = "MEDIUM"
  ): TradingAdvice {
    const { tradingSignal, marketCondition, keyLevels } = analysisResult;

    // 计算建议数量
    const riskPercentage = this.getRiskPercentage(riskTolerance);
    const riskAmount = toNumber(
      multiply(accountBalance, divide(riskPercentage, 100))
    );
    const stopLossDistance = toNumber(
      abs(subtract(tradingSignal.entryPrice, tradingSignal.stopLoss))
    );
    const quantity =
      stopLossDistance > 0 ? toNumber(divide(riskAmount, stopLossDistance)) : 0;

    // 确定风险等级
    const riskLevel = this.assessRiskLevel(analysisResult);

    // 生成推理说明
    const reasoning = this.generateReasoning(analysisResult);

    return {
      action:
        tradingSignal.direction === "HOLD"
          ? "HOLD"
          : tradingSignal.direction === "LONG"
          ? "BUY"
          : "SELL",
      entryPrice: tradingSignal.entryPrice,
      quantity: toNumber(max(quantity, 0)),
      stopLoss: tradingSignal.stopLoss,
      takeProfit: tradingSignal.takeProfit,
      timeframe: this.determineTimeframe(analysisResult),
      confidence: tradingSignal.confidence,
      reasoning,
      riskLevel,
    };
  }

  /**
   * 生成仓位管理策略
   */
  generatePositionManagement(
    analysisResult: AIAnalysisResult,
    rollingStrategy: RollingStrategy
  ): PositionManagement {
    const { tradingSignal, keyLevels } = analysisResult;

    // 计算加仓位置
    const addPositions = this.calculateAddPositions(
      tradingSignal.entryPrice,
      keyLevels,
      rollingStrategy
    );

    // 计算分批止盈
    const partialExits = this.calculatePartialExits(
      tradingSignal.entryPrice,
      tradingSignal.takeProfit,
      tradingSignal.direction
    );

    return {
      initialPosition: rollingStrategy.basePosition,
      addPositions,
      exitStrategy: {
        partialExits,
        stopLoss: tradingSignal.stopLoss,
        trailingStop: tradingSignal.confidence > 80,
      },
    };
  }

  /**
   * 分析趋势
   */
  analyzeTrend(data: ProcessedKlineData[]): TrendAnalysis {
    if (data.length < 20) {
      return {
        direction: "SIDEWAYS",
        strength: 1,
        duration: 0,
        breakoutPotential: 50,
        volumeConfirmation: false,
      };
    }

    // 计算趋势方向
    const recentData = data.slice(-20);
    const priceChange = toNumber(
      subtract(recentData[recentData.length - 1].close, recentData[0].close)
    );
    const priceChangePercent = toNumber(
      multiply(divide(priceChange, recentData[0].close), 100)
    );

    let direction: "UP" | "DOWN" | "SIDEWAYS" = "SIDEWAYS";
    if (gt(abs(priceChangePercent), 2)) {
      direction = priceChangePercent > 0 ? "UP" : "DOWN";
    }

    // 计算趋势强度 (1-10)
    const strength = toNumber(min(divide(abs(priceChangePercent), 2), 10));

    // 计算持续时间
    const duration = this.calculateTrendDuration(data);

    // 计算突破潜力
    const breakoutPotential = this.calculateBreakoutPotential(data);

    // 成交量确认
    const volumeConfirmation = this.checkVolumeConfirmation(data);

    return {
      direction,
      strength,
      duration,
      breakoutPotential,
      volumeConfirmation,
    };
  }

  /**
   * 检查滚仓条件 - 优化版本
   */
  checkRollingConditions(
    analysisResult: AIAnalysisResult,
    currentPrice: number,
    entryPrice: number,
    currentPosition: number
  ): {
    shouldAdd: boolean;
    shouldReduce: boolean;
    addSize: number;
    reason: string;
  } {
    const { tradingSignal, marketTrend, marketCondition } = analysisResult;
    const priceChange = (currentPrice - entryPrice) / entryPrice;
    const isProfit =
      (tradingSignal.direction === "LONG" && priceChange > 0) ||
      (tradingSignal.direction === "SHORT" && priceChange < 0);

    // 动态调整加仓阈值
    let addThreshold = 0.015; // 基础1.5%
    if (tradingSignal.confidence > 85) addThreshold = 0.01; // 高信心度降低到1%
    if (marketCondition.volatility === "HIGH") addThreshold = 0.02; // 高波动提高到2%

    // 低波动熊市特殊处理
    if (
      marketCondition.volatility === "LOW" &&
      marketTrend.shortTerm === "BEARISH"
    ) {
      addThreshold = 0.025; // 低波动熊市提高到2.5%
    }

    // 动态调整止损阈值
    let stopThreshold = 0.02; // 基础2%
    if (tradingSignal.confidence < 60) stopThreshold = 0.015; // 低信心度收紧到1.5%
    if (marketCondition.volatility === "HIGH") stopThreshold = 0.025; // 高波动放宽到2.5%

    // 低波动熊市更严格的止损
    if (
      marketCondition.volatility === "LOW" &&
      marketTrend.shortTerm === "BEARISH"
    ) {
      stopThreshold = 0.01; // 低波动熊市收紧到1%
    }

    // 加仓条件检查 - 更保守的策略（降低回撤）
    let shouldAdd =
      isProfit &&
      Math.abs(priceChange) > addThreshold &&
      tradingSignal.confidence > 75 && // 提高信心度要求
      marketTrend.shortTerm !== "NEUTRAL" &&
      currentPosition < 12; // 降低最大仓位到12%

    // 低波动熊市环境下更严格的加仓条件
    if (
      marketCondition.volatility === "LOW" &&
      marketTrend.shortTerm === "BEARISH"
    ) {
      shouldAdd =
        shouldAdd &&
        tradingSignal.confidence > 85 && // 极高信心度要求
        currentPosition < 8; // 更低的最大仓位
    }

    // 减仓条件检查 - 更严格的风控
    const shouldReduce = !isProfit && Math.abs(priceChange) > stopThreshold;

    // 动态加仓大小（优化版 - 降低回撤）
    let addSize = 0;
    if (shouldAdd) {
      if (tradingSignal.confidence > 90) {
        addSize = 2.5; // 极高信心度加仓2.5%
      } else if (tradingSignal.confidence > 80) {
        addSize = 2; // 高信心度加仓2%
      } else {
        addSize = 1.5; // 中等信心度加仓1.5%
      }

      // 根据市场波动率调整加仓大小
      if (marketCondition.volatility === "HIGH") {
        addSize *= 0.7; // 高波动时减少加仓
      }
    }

    let reason = "";
    if (shouldAdd) {
      reason = `趋势确认，盈利${(Math.abs(priceChange) * 100).toFixed(
        1
      )}%，信心度${tradingSignal.confidence}%，建议加仓${addSize}%`;
    } else if (shouldReduce) {
      reason = `亏损${(Math.abs(priceChange) * 100).toFixed(
        1
      )}%，触发动态止损，建议减仓`;
    } else {
      reason = "维持当前仓位，等待更好时机";
    }

    return {
      shouldAdd,
      shouldReduce,
      addSize,
      reason,
    };
  }

  /**
   * 计算风险收益比
   */
  calculateRiskReward(
    entryPrice: number,
    stopLoss: number,
    takeProfit: number[]
  ): number {
    const risk = Math.abs(entryPrice - stopLoss);
    const reward =
      takeProfit.length > 0 ? Math.abs(takeProfit[0] - entryPrice) : risk * 2;

    return risk > 0 ? reward / risk : 0;
  }

  /**
   * 动态止盈策略 - 新增功能
   */
  calculateDynamicTakeProfit(
    entryPrice: number,
    currentPrice: number,
    direction: "LONG" | "SHORT",
    confidence: number,
    volatility: "HIGH" | "MEDIUM" | "LOW"
  ): {
    trailingStopPrice: number;
    nextTakeProfitLevel: number;
    shouldPartialExit: boolean;
    exitPercentage: number;
  } {
    const priceChange = (currentPrice - entryPrice) / entryPrice;
    const isProfit =
      (direction === "LONG" && priceChange > 0) ||
      (direction === "SHORT" && priceChange < 0);

    if (!isProfit) {
      return {
        trailingStopPrice: entryPrice,
        nextTakeProfitLevel: entryPrice,
        shouldPartialExit: false,
        exitPercentage: 0,
      };
    }

    const profitPercent = Math.abs(priceChange);

    // 动态调整止盈阈值（优化版 - 更保守）
    let takeProfitThreshold = 0.02; // 基础降低到2%
    if (confidence > 85) takeProfitThreshold = 0.03; // 高信心度提高到3%
    if (volatility === "HIGH") takeProfitThreshold = 0.025; // 高波动调整到2.5%
    if (volatility === "LOW") takeProfitThreshold = 0.015; // 低波动降低到1.5%

    // 计算追踪止损价格（优化版 - 更严格）
    let trailingStopDistance = 0.008; // 基础0.8%
    if (volatility === "HIGH") trailingStopDistance = 0.012; // 高波动1.2%
    if (volatility === "LOW") trailingStopDistance = 0.006; // 低波动0.6%

    const trailingStopPrice =
      direction === "LONG"
        ? currentPrice * (1 - trailingStopDistance)
        : currentPrice * (1 + trailingStopDistance);

    // 分批止盈逻辑（优化版 - 更保守）
    let shouldPartialExit = false;
    let exitPercentage = 0;

    if (profitPercent >= takeProfitThreshold) {
      shouldPartialExit = true;
      if (profitPercent >= 0.06) {
        // 6%以上利润（降低门槛）
        exitPercentage = 60; // 提高止盈比例到60%
      } else if (profitPercent >= 0.04) {
        // 4%以上利润（降低门槛）
        exitPercentage = 40; // 止盈40%
      } else if (profitPercent >= 0.02) {
        // 2%以上利润（新增更早止盈）
        exitPercentage = 25; // 止盈25%
      }
    }

    const nextTakeProfitLevel =
      direction === "LONG"
        ? entryPrice * (1 + takeProfitThreshold * 1.5)
        : entryPrice * (1 - takeProfitThreshold * 1.5);

    return {
      trailingStopPrice,
      nextTakeProfitLevel,
      shouldPartialExit,
      exitPercentage,
    };
  }

  /**
   * 私有方法：获取风险百分比（优化版 - 降低回撤）
   */
  private getRiskPercentage(riskTolerance: string): number {
    const riskMap: Record<string, number> = {
      LOW: 0.8, // 降低保守型风险
      MEDIUM: 1.5, // 降低平衡型风险
      HIGH: 2.5, // 降低激进型风险
    };
    return riskMap[riskTolerance] || 1.5;
  }

  /**
   * 私有方法：评估风险等级
   */
  private assessRiskLevel(
    analysisResult: AIAnalysisResult
  ): "LOW" | "MEDIUM" | "HIGH" {
    const { marketCondition, tradingSignal } = analysisResult;

    let riskScore = 0;

    // 波动率风险
    if (marketCondition.volatility === "HIGH") riskScore += 2;
    else if (marketCondition.volatility === "MEDIUM") riskScore += 1;

    // 信心度风险
    if (tradingSignal.confidence < 60) riskScore += 2;
    else if (tradingSignal.confidence < 80) riskScore += 1;

    // 杠杆风险
    if (tradingSignal.leverage > 5) riskScore += 2;
    else if (tradingSignal.leverage > 2) riskScore += 1;

    if (riskScore >= 4) return "HIGH";
    if (riskScore >= 2) return "MEDIUM";
    return "LOW";
  }

  /**
   * 私有方法：生成推理说明
   */
  private generateReasoning(analysisResult: AIAnalysisResult): string {
    const { tradingSignal, marketTrend, marketCondition } = analysisResult;

    let reasoning = `基于多时间维度分析，`;

    // 趋势分析
    if (marketTrend.shortTerm === marketTrend.mediumTerm) {
      reasoning += `短中期趋势一致向${
        marketTrend.shortTerm === "BULLISH" ? "上" : "下"
      }，`;
    } else {
      reasoning += `短期${marketTrend.shortTerm}，中期${marketTrend.mediumTerm}，`;
    }

    // 市场条件
    reasoning += `当前市场波动率${marketCondition.volatility}，成交量${marketCondition.volume}。`;

    // 信心度
    if (tradingSignal.confidence > 80) {
      reasoning += `技术指标强烈支持该方向，建议积极参与。`;
    } else if (tradingSignal.confidence > 60) {
      reasoning += `技术指标支持该方向，建议谨慎参与。`;
    } else {
      reasoning += `信号不够明确，建议观望等待更好机会。`;
    }

    return reasoning;
  }

  /**
   * 私有方法：确定时间框架（基于极短期分析）
   */
  private determineTimeframe(analysisResult: AIAnalysisResult): string {
    const { marketTrend, tradingSignal, multiTimeframePrediction } =
      analysisResult;

    // 优先使用极短期预测来确定持有时间
    if (
      multiTimeframePrediction?.overallAssessment?.primaryTimeframe ===
      "ultraShort"
    ) {
      return "极短期交易 (15分钟-2小时)";
    }

    // 基于极短期趋势（现在对应shortTerm）来判断
    if (marketTrend.shortTerm !== "NEUTRAL") {
      return "极短期交易 (15分钟-2小时)";
    } else if (marketTrend.mediumTerm !== "NEUTRAL") {
      return "短期交易 (2-8小时)";
    } else if (marketTrend.longTerm !== "NEUTRAL") {
      return "中期持有 (8-24小时)";
    } else {
      return "极短期交易 (15分钟-2小时)";
    }
  }

  /**
   * 私有方法：计算加仓位置（优化版 - 降低回撤）
   */
  private calculateAddPositions(
    entryPrice: number,
    keyLevels: any,
    rollingStrategy: RollingStrategy
  ) {
    const addPositions = [];
    const threshold = rollingStrategy.additionThreshold / 100;

    // 向上加仓位置（减少加仓次数和大小）
    for (let i = 1; i <= 2; i++) {
      const addSize = i === 1 ? 2 : 1.5; // 第一次加仓2%，第二次1.5%
      addPositions.push({
        price: entryPrice * (1 + threshold * i),
        size: addSize,
        condition: `价格突破${(entryPrice * (1 + threshold * i)).toFixed(
          4
        )}且成交量确认，严格风控`,
      });
    }

    return addPositions;
  }

  /**
   * 私有方法：计算分批止盈（优化版 - 更保守）
   */
  private calculatePartialExits(
    entryPrice: number,
    takeProfit: number[],
    direction: string
  ) {
    const partialExits = [];

    if (takeProfit.length > 0) {
      // 第一次止盈 - 40%仓位（提前锁定利润）
      partialExits.push({
        price: takeProfit[0],
        percentage: 40,
      });

      // 第二次止盈 - 35%仓位
      if (takeProfit.length > 1) {
        partialExits.push({
          price: takeProfit[1],
          percentage: 35,
        });
      }

      // 第三次止盈 - 剩余25%（降低风险敞口）
      if (takeProfit.length > 2) {
        partialExits.push({
          price: takeProfit[2],
          percentage: 25,
        });
      }
    }

    return partialExits;
  }

  /**
   * 私有方法：计算趋势持续时间
   */
  private calculateTrendDuration(data: ProcessedKlineData[]): number {
    // 简化实现：计算连续同方向变化的周期数
    let duration = 0;
    const recent = data.slice(-10);

    for (let i = recent.length - 1; i > 0; i--) {
      const currentChange = recent[i].changePercent;
      const prevChange = recent[i - 1].changePercent;

      if (
        (currentChange > 0 && prevChange > 0) ||
        (currentChange < 0 && prevChange < 0)
      ) {
        duration++;
      } else {
        break;
      }
    }

    return duration;
  }

  /**
   * 私有方法：计算突破潜力
   */
  private calculateBreakoutPotential(data: ProcessedKlineData[]): number {
    const recent = data.slice(-20);
    const priceRange =
      Math.max(...recent.map((d) => d.high)) -
      Math.min(...recent.map((d) => d.low));
    const currentPrice = recent[recent.length - 1].close;
    const rangePosition =
      (currentPrice - Math.min(...recent.map((d) => d.low))) / priceRange;

    // 接近区间边界时突破潜力更高
    if (rangePosition > 0.8 || rangePosition < 0.2) {
      return 80;
    } else if (rangePosition > 0.6 || rangePosition < 0.4) {
      return 60;
    } else {
      return 40;
    }
  }

  /**
   * 私有方法：检查成交量确认
   */
  private checkVolumeConfirmation(data: ProcessedKlineData[]): boolean {
    if (data.length < 5) return false;

    const recent = data.slice(-5);
    const avgVolume =
      recent.slice(0, -1).reduce((sum, d) => sum + d.volume, 0) / 4;
    const latestVolume = recent[recent.length - 1].volume;

    return latestVolume > avgVolume * 1.2; // 成交量放大20%以上
  }
}

// 导出单例实例
export const strategyService = TradingStrategyService.getInstance();
