/**
 * 服务端预测跟踪器 - 使用Prisma + SQLite3存储
 */

import { prisma } from "./database";
import { dataService } from "./data-service";

export interface PredictionData {
  symbol: string;
  direction: "BUY" | "SELL" | "HOLD";
  entryPrice: number;
  targetPrice: number;
  stopLoss: number;
  timeframe: string;
  confidence: number;
  reasoning: string;
  // 新增：价格范围验证配置
  priceRangeValidation?: {
    enabled: boolean;
    samplingInterval: number; // 采样间隔（分钟）
    tolerancePercent: number; // 价格容忍度百分比
  };
}

export interface AccuracyStatsResult {
  totalPredictions: number;
  verifiedPredictions: number;
  directionAccuracy: number;
  priceAccuracy: number;
  overallAccuracy: number;
  byTimeframe: Record<string, number>;
  bySymbol: Record<string, number>;
  byConfidence: Record<string, number>;
  recentTrend: number;
}

export interface DetailedStatsResult {
  hitTargetRate: number;
  hitStopLossRate: number;
  avgProfitLoss: number;
  bestPerformingStrategy: string;
  worstPerformingStrategy: string;
  marketConditionAnalysis: string;
  riskMetrics: {
    maxDrawdown: number;
    sharpeRatio: number;
    winRate: number;
  };
  performanceTrends: {
    last7Days: number;
    last30Days: number;
    improvement: number;
  };
  // 新增：范围验证统计
  rangeValidationStats: {
    rangeHitRate: number;
    avgTimeToTarget: number; // 平均达到目标的时间（分钟）
    bestPerformanceInRange: number; // 范围内最佳表现
    rangeVolatility: number; // 范围内价格波动率
  };
}

// 新增：价格采样记录接口
export interface PriceSample {
  timestamp: Date;
  price: number;
  priceChangePercent: number;
  hitTarget: boolean;
  hitStopLoss: boolean;
}

// 新增：范围验证结果接口
export interface RangeValidationResult {
  samples: PriceSample[];
  bestPrice: number;
  worstPrice: number;
  avgPrice: number;
  hitTargetInRange: boolean;
  hitStopLossInRange: boolean;
  timeToTarget?: number; // 达到目标的时间（分钟）
  timeToStopLoss?: number; // 触及止损的时间（分钟）
  rangeScore: number; // 范围内综合评分
  validationStartTime: Date; // 验证开始时间
  validationEndTime: Date; // 验证结束时间
  actualDataPoints: number; // 实际获取的数据点数量
}

export class ServerPredictionTracker {
  private static instance: ServerPredictionTracker;

  static getInstance(): ServerPredictionTracker {
    if (!ServerPredictionTracker.instance) {
      ServerPredictionTracker.instance = new ServerPredictionTracker();
    }
    return ServerPredictionTracker.instance;
  }

  /**
   * 记录新的预测
   */
  async recordPrediction(data: PredictionData): Promise<string> {
    try {
      const record = await prisma.predictionRecord.create({
        data: {
          symbol: data.symbol,
          direction: data.direction,
          entryPrice: data.entryPrice,
          targetPrice: data.targetPrice,
          stopLoss: data.stopLoss,
          timeframe: data.timeframe,
          confidence: data.confidence,
          reasoning: data.reasoning,
        },
      });

      console.log(`✅ 预测记录已保存: ${record.id}`);

      // 解析预测数据以获取范围验证配置
      let predictionData: any = {};
      try {
        predictionData = JSON.parse(data.reasoning || "{}");
      } catch (error) {
        console.warn("解析预测reasoning失败，使用默认配置");
      }

      // 创建验证任务
      await this.scheduleValidation(record.id, data.timeframe, predictionData);

      return record.id;
    } catch (error) {
      console.error("❌ 记录预测失败:", error);
      throw error;
    }
  }

  /**
   * 验证单个预测（支持范围验证）
   */
  async verifyPrediction(predictionId: string): Promise<void> {
    try {
      const record = await prisma.predictionRecord.findUnique({
        where: { id: predictionId },
      });

      if (!record || record.verifiedAt) {
        return; // 记录不存在或已验证
      }

      // 检查是否启用范围验证
      const predictionData = JSON.parse(record.reasoning || "{}");
      const rangeValidationEnabled =
        predictionData.priceRangeValidation?.enabled;

      let result: any;
      let rangeValidationResult: RangeValidationResult | null = null;

      if (rangeValidationEnabled) {
        // 执行范围验证
        rangeValidationResult = await this.performRangeValidation(
          record,
          predictionData.priceRangeValidation
        );
        result = this.calculateRangeBasedResult(record, rangeValidationResult);
      } else {
        // 传统单点验证
        const currentPrice = await this.getCurrentPrice(record.symbol);
        result = this.calculatePredictionResult(record, currentPrice);
      }

      // 更新记录
      await prisma.predictionRecord.update({
        where: { id: predictionId },
        data: {
          actualPrice: result.actualPrice || result.currentPrice,
          actualDirection: result.actualDirection,
          priceChange: result.priceChange,
          priceChangePercent: result.priceChangePercent,
          hitTarget: result.hitTarget,
          partialHitTarget: result.partialHitTarget,
          hitStopLoss: result.hitStopLoss,
          directionCorrect: result.directionCorrect,
          priceAccuracy: result.priceAccuracy,
          overallScore: result.overallScore,
          verifiedAt: new Date(),
          // 存储范围验证结果
          reasoning: rangeValidationResult
            ? JSON.stringify({
                ...predictionData,
                rangeValidationResult,
              })
            : record.reasoning,
        },
      });

      console.log(
        `✅ 预测验证完成: ${predictionId}, 评分: ${result.overallScore.toFixed(
          1
        )}${rangeValidationEnabled ? " (范围验证)" : ""}`
      );

      // 更新验证任务状态
      await prisma.validationTask.updateMany({
        where: { predictionId, status: "PENDING" },
        data: {
          status: "COMPLETED",
          executedAt: new Date(),
        },
      });
    } catch (error) {
      console.error(`❌ 验证预测失败 ${predictionId}:`, error);

      // 更新验证任务状态为失败
      await prisma.validationTask.updateMany({
        where: { predictionId, status: "PENDING" },
        data: {
          status: "FAILED",
          executedAt: new Date(),
          errorMessage: error instanceof Error ? error.message : "未知错误",
        },
      });
    }
  }

  /**
   * 批量验证到期的预测
   */
  async batchVerifyPredictions(): Promise<number> {
    try {
      // 获取待验证的任务
      const pendingTasks = await prisma.validationTask.findMany({
        where: {
          status: "PENDING",
          scheduledAt: {
            lte: new Date(),
          },
        },
        take: 50, // 限制批量处理数量
      });

      console.log(`🔍 发现 ${pendingTasks.length} 个待验证的预测`);

      let verifiedCount = 0;
      for (const task of pendingTasks) {
        try {
          await this.verifyPrediction(task.predictionId);
          verifiedCount++;
        } catch (error) {
          console.error(`验证任务失败 ${task.id}:`, error);
        }
      }

      // 更新统计数据
      if (verifiedCount > 0) {
        await this.updateAccuracyStats();
      }

      return verifiedCount;
    } catch (error) {
      console.error("❌ 批量验证失败:", error);
      return 0;
    }
  }

  /**
   * 获取准确率统计
   */
  async getAccuracyStats(
    symbol?: string,
    timeframe?: string
  ): Promise<AccuracyStatsResult> {
    try {
      const whereClause: any = {};
      if (symbol) whereClause.symbol = symbol;
      if (timeframe) whereClause.timeframe = timeframe;

      // 获取总预测数
      const totalPredictions = await prisma.predictionRecord.count({
        where: whereClause,
      });

      // 获取已验证预测
      const verifiedRecords = await prisma.predictionRecord.findMany({
        where: {
          ...whereClause,
          verifiedAt: { not: null },
        },
      });

      if (verifiedRecords.length === 0) {
        return this.getEmptyStats();
      }

      // 计算准确率
      const directionCorrectCount = verifiedRecords.filter(
        (r) => r.directionCorrect
      ).length;
      const directionAccuracy =
        (directionCorrectCount / verifiedRecords.length) * 100;

      const priceAccuracy =
        verifiedRecords.reduce((sum, r) => sum + (r.priceAccuracy || 0), 0) /
        verifiedRecords.length;
      const overallAccuracy =
        verifiedRecords.reduce((sum, r) => sum + (r.overallScore || 0), 0) /
        verifiedRecords.length;

      // 按时间框架统计
      const byTimeframe = await this.getStatsByField(
        verifiedRecords,
        "timeframe"
      );

      // 按币种统计
      const bySymbol = await this.getStatsByField(verifiedRecords, "symbol");

      // 按信心度统计
      const byConfidence = await this.getStatsByConfidence(verifiedRecords);

      // 计算最近趋势
      const recentTrend = await this.calculateRecentTrend(verifiedRecords);

      return {
        totalPredictions,
        verifiedPredictions: verifiedRecords.length,
        directionAccuracy,
        priceAccuracy,
        overallAccuracy,
        byTimeframe,
        bySymbol,
        byConfidence,
        recentTrend,
      };
    } catch (error) {
      console.error("❌ 获取准确率统计失败:", error);
      return this.getEmptyStats();
    }
  }

  /**
   * 获取改进建议（优化版）
   */
  async getImprovementSuggestions(): Promise<string[]> {
    const stats = await this.getAccuracyStats();
    const detailedStats = await this.getDetailedStats();
    const suggestions: string[] = [];

    // 达标率分析（重点关注）
    if (detailedStats.hitTargetRate < 50) {
      suggestions.push(
        "达标率偏低，建议降低目标价格设置，采用更保守的止盈策略"
      );
    } else if (detailedStats.hitTargetRate < 70) {
      suggestions.push("达标率中等，可考虑优化止盈位设置，采用分批止盈策略");
    }

    // 止损率分析
    if (detailedStats.hitStopLossRate > 15) {
      suggestions.push("止损触发率过高，建议收紧止损位设置，提高风险控制");
    }

    // 基础准确率分析
    if (stats.directionAccuracy < 70) {
      suggestions.push(
        "方向预测准确率偏低，建议优化趋势分析算法和技术指标权重"
      );
    }

    if (stats.priceAccuracy < 80) {
      suggestions.push(
        "价格预测准确率偏低，建议改进支撑阻力位计算和波动率模型"
      );
    }

    // 盈亏比分析
    if (detailedStats.avgProfitLoss < 0) {
      suggestions.push("平均盈亏为负，建议优化仓位管理和风险控制策略");
    }

    // 回撤分析
    if (detailedStats.riskMetrics.maxDrawdown > 20) {
      suggestions.push(
        "最大回撤过大，建议降低单次仓位大小，采用更保守的加仓策略"
      );
    }

    if (stats.overallAccuracy < 70) {
      suggestions.push("综合准确率偏低，建议全面检查AI模型参数和市场数据质量");
    }

    // 趋势分析
    if (stats.recentTrend < -5) {
      suggestions.push(
        "最近预测准确率下降明显，建议检查市场环境变化和模型适应性"
      );
    } else if (stats.recentTrend < -2) {
      suggestions.push("预测准确率略有下降，建议关注市场波动性变化");
    } else if (stats.recentTrend > 5) {
      suggestions.push(
        "预测准确率显著提升，当前策略表现良好，可考虑适度提高信心度"
      );
    }

    // 时间框架分析
    const timeframeEntries = Object.entries(stats.byTimeframe).sort(
      ([, a], [, b]) => a - b
    );
    if (timeframeEntries.length > 0) {
      const worstTimeframe = timeframeEntries[0];
      const bestTimeframe = timeframeEntries[timeframeEntries.length - 1];

      if (worstTimeframe[1] < 50) {
        suggestions.push(
          `${worstTimeframe[0]}时间框架预测效果较差(${worstTimeframe[1].toFixed(
            1
          )}%)，建议调整该时间段的分析策略`
        );
      }

      if (bestTimeframe[1] > 80) {
        suggestions.push(
          `${bestTimeframe[0]}时间框架表现优秀(${bestTimeframe[1].toFixed(
            1
          )}%)，可将其策略应用到其他时间框架`
        );
      }
    }

    // 币种表现分析
    const symbolEntries = Object.entries(stats.bySymbol).sort(
      ([, a], [, b]) => a - b
    );
    if (symbolEntries.length > 0) {
      const worstSymbol = symbolEntries[0];
      const bestSymbol = symbolEntries[symbolEntries.length - 1];

      if (worstSymbol[1] < 55) {
        suggestions.push(
          `${worstSymbol[0]}预测准确率较低(${worstSymbol[1].toFixed(
            1
          )}%)，建议针对该币种优化分析模型`
        );
      }

      if (symbolEntries.length > 1 && bestSymbol[1] - worstSymbol[1] > 20) {
        suggestions.push(
          `不同币种预测准确率差异较大，建议为不同币种制定差异化策略`
        );
      }
    }

    // 信心度分析
    const confidenceEntries = Object.entries(stats.byConfidence);
    const highConfidence = confidenceEntries.find(([key]) =>
      key.includes("高信心")
    );
    const lowConfidence = confidenceEntries.find(([key]) =>
      key.includes("低信心")
    );

    if (highConfidence && highConfidence[1] < 75) {
      suggestions.push(
        `高信心预测准确率偏低(${highConfidence[1].toFixed(
          1
        )}%)，建议重新校准信心度评估标准`
      );
    }

    if (lowConfidence && lowConfidence[1] > 60) {
      suggestions.push(
        `低信心预测准确率较高(${lowConfidence[1].toFixed(
          1
        )}%)，可能存在信心度评估过于保守的问题`
      );
    }

    return suggestions;
  }

  /**
   * 获取详细统计信息
   */
  async getDetailedStats(): Promise<DetailedStatsResult> {
    try {
      const verifiedRecords = await prisma.predictionRecord.findMany({
        where: {
          verifiedAt: { not: null },
        },
        orderBy: { verifiedAt: "desc" },
      });

      if (verifiedRecords.length === 0) {
        return this.getEmptyDetailedStats();
      }

      // 计算基础指标（优化版 - 包含部分达标）
      const hitTargetCount = verifiedRecords.filter((r) => r.hitTarget).length;
      const partialHitTargetCount = verifiedRecords.filter(
        (r) => r.partialHitTarget
      ).length;
      const combinedTargetCount = verifiedRecords.filter(
        (r) => r.hitTarget || r.partialHitTarget
      ).length;

      const hitStopLossCount = verifiedRecords.filter(
        (r) => r.hitStopLoss
      ).length;
      const winCount = verifiedRecords.filter(
        (r) => r.overallScore && r.overallScore > 50 // 降低胜率门槛
      ).length;

      const hitTargetRate =
        (combinedTargetCount / verifiedRecords.length) * 100; // 使用组合达标率
      const hitStopLossRate = (hitStopLossCount / verifiedRecords.length) * 100;
      const winRate = (winCount / verifiedRecords.length) * 100;

      // 计算平均盈亏
      const profitLossValues = verifiedRecords
        .filter((r) => r.priceChangePercent !== null)
        .map((r) => r.priceChangePercent!);
      const avgProfitLoss =
        profitLossValues.length > 0
          ? profitLossValues.reduce((sum, val) => sum + val, 0) /
            profitLossValues.length
          : 0;

      // 计算最大回撤
      const maxDrawdown = this.calculateMaxDrawdown(verifiedRecords);

      // 计算夏普比率
      const sharpeRatio = this.calculateSharpeRatio(profitLossValues);

      // 分析最佳和最差策略
      const strategyAnalysis = this.analyzeStrategies(verifiedRecords);

      // 计算时间趋势
      const performanceTrends =
        this.calculatePerformanceTrends(verifiedRecords);

      // 市场环境分析
      const marketConditionAnalysis =
        this.analyzeMarketConditions(verifiedRecords);

      // 计算范围验证统计
      const rangeValidationStats =
        this.calculateRangeValidationStats(verifiedRecords);

      return {
        hitTargetRate,
        hitStopLossRate,
        avgProfitLoss,
        bestPerformingStrategy: strategyAnalysis.best,
        worstPerformingStrategy: strategyAnalysis.worst,
        marketConditionAnalysis,
        riskMetrics: {
          maxDrawdown,
          sharpeRatio,
          winRate,
        },
        performanceTrends,
        rangeValidationStats,
      };
    } catch (error) {
      console.error("获取详细统计失败:", error);
      return this.getEmptyDetailedStats();
    }
  }

  /**
   * 清理过期数据
   */
  async cleanupOldData(daysToKeep: number = 90): Promise<void> {
    try {
      const cutoffDate = new Date();
      cutoffDate.setDate(cutoffDate.getDate() - daysToKeep);

      // 删除过期的预测记录
      const deletedPredictions = await prisma.predictionRecord.deleteMany({
        where: {
          timestamp: {
            lt: cutoffDate,
          },
        },
      });

      // 删除过期的数据质量记录
      const deletedQualityRecords = await prisma.dataQualityRecord.deleteMany({
        where: {
          timestamp: {
            lt: cutoffDate,
          },
        },
      });

      // 删除过期的验证任务
      const deletedTasks = await prisma.validationTask.deleteMany({
        where: {
          createdAt: {
            lt: cutoffDate,
          },
        },
      });

      console.log(
        `🧹 数据清理完成: 删除了 ${deletedPredictions.count} 个预测记录, ${deletedQualityRecords.count} 个质量记录, ${deletedTasks.count} 个验证任务`
      );
    } catch (error) {
      console.error("❌ 数据清理失败:", error);
    }
  }

  // 私有方法
  private async scheduleValidation(
    predictionId: string,
    timeframe: string,
    predictionData?: any
  ): Promise<void> {
    const now = new Date();
    const timeframeMs = this.parseTimeframe(timeframe);

    // 计划执行时间 = 当前时间 + 时间框架
    const scheduledAt = new Date(now.getTime() + timeframeMs);

    // 计算验证时间区间
    const { validationStartTime, validationEndTime } =
      this.calculateValidationTimeRange(timeframe, now);

    // 从预测数据中获取范围验证配置
    let samplingInterval = 5; // 默认5分钟
    let tolerancePercent = 0.5; // 默认0.5%

    if (predictionData?.priceRangeValidation) {
      samplingInterval =
        predictionData.priceRangeValidation.samplingInterval ||
        samplingInterval;
      tolerancePercent =
        predictionData.priceRangeValidation.tolerancePercent ||
        tolerancePercent;
    }

    await prisma.validationTask.create({
      data: {
        predictionId,
        scheduledAt,
        // 存储验证时间区间信息
        validationStartTime,
        validationEndTime,
        timeframe,
        samplingInterval,
        tolerancePercent,
      },
    });

    console.log(`📅 验证任务已安排: ${predictionId}`);
    console.log(`   执行时间: ${scheduledAt.toLocaleString()}`);
    console.log(
      `   验证区间: ${validationStartTime.toLocaleString()} - ${validationEndTime.toLocaleString()}`
    );
    console.log(`   采样间隔: ${samplingInterval}分钟`);
    console.log(`   容忍度: ${tolerancePercent}%`);
  }

  private async getCurrentPrice(symbol: string): Promise<number> {
    try {
      const coinInfo = await dataService.getCoinInfo(symbol);
      return coinInfo[0]?.price || 0;
    } catch (error) {
      console.error(`获取 ${symbol} 价格失败:`, error);
      return 0;
    }
  }

  /**
   * 获取历史价格数据
   * 在实际应用中，这里应该调用真实的历史数据API
   */
  private async getHistoricalPrices(
    symbol: string,
    startTime: Date,
    endTime: Date,
    intervalMinutes: number
  ): Promise<{ timestamp: Date; price: number }[]> {
    try {
      // 这里应该调用真实的历史数据API，比如币安、火币等
      // 目前作为示例，我们模拟一些历史价格数据

      console.log(`📊 获取历史价格数据: ${symbol}`);
      console.log(
        `   时间范围: ${startTime.toLocaleString()} - ${endTime.toLocaleString()}`
      );
      console.log(`   采样间隔: ${intervalMinutes}分钟`);

      // 模拟历史价格数据生成
      const historicalPrices: { timestamp: Date; price: number }[] = [];
      const currentPrice = await this.getCurrentPrice(symbol);

      if (currentPrice === 0) {
        console.warn(`⚠️ 无法获取 ${symbol} 的当前价格`);
        return [];
      }

      // 生成时间序列
      let currentTime = new Date(startTime);
      const intervalMs = intervalMinutes * 60 * 1000;

      while (currentTime <= endTime) {
        // 模拟价格波动（实际应用中应该从API获取真实数据）
        const randomFactor = 0.98 + Math.random() * 0.04; // ±2% 随机波动
        const simulatedPrice = currentPrice * randomFactor;

        historicalPrices.push({
          timestamp: new Date(currentTime),
          price: simulatedPrice,
        });

        currentTime = new Date(currentTime.getTime() + intervalMs);
      }

      console.log(`✅ 获取到 ${historicalPrices.length} 个历史价格数据点`);

      // TODO: 在实际应用中，替换为真实的API调用
      // 例如：
      // const response = await fetch(`https://api.binance.com/api/v3/klines?symbol=${symbol}&interval=${interval}&startTime=${startTime.getTime()}&endTime=${endTime.getTime()}`);
      // const klines = await response.json();
      // return klines.map(kline => ({
      //   timestamp: new Date(kline[0]),
      //   price: parseFloat(kline[4]) // 收盘价
      // }));

      return historicalPrices;
    } catch (error) {
      console.error(`获取 ${symbol} 历史价格失败:`, error);
      return [];
    }
  }

  private calculatePredictionResult(record: any, currentPrice: number) {
    const priceChange = currentPrice - record.entryPrice;
    const priceChangePercent = (priceChange / record.entryPrice) * 100;

    // 优化方向判断逻辑 - 根据时间框架调整阈值
    let actualDirection: "UP" | "DOWN" | "SIDEWAYS";
    let directionThreshold = 0.3; // 默认阈值

    // 根据时间框架调整判断阈值
    if (
      record.timeframe.includes("15分钟") ||
      record.timeframe.includes("1小时")
    ) {
      directionThreshold = 0.2; // 极短期交易，降低阈值
    } else if (
      record.timeframe.includes("2小时") ||
      record.timeframe.includes("8小时")
    ) {
      directionThreshold = 0.5; // 短期交易，中等阈值
    } else {
      directionThreshold = 0.8; // 中长期交易，提高阈值
    }

    if (Math.abs(priceChangePercent) < directionThreshold) {
      actualDirection = "SIDEWAYS";
    } else if (priceChange > 0) {
      actualDirection = "UP";
    } else {
      actualDirection = "DOWN";
    }

    // 检查是否达到目标或止损（优化版 - 考虑部分达标）
    const targetDistance =
      Math.abs(currentPrice - record.targetPrice) / record.targetPrice;
    const partialTargetThreshold = 0.5; // 达到目标价格50%距离也算部分达标

    const hitTarget =
      record.direction === "BUY"
        ? currentPrice >= record.targetPrice
        : currentPrice <= record.targetPrice;

    // 新增：部分达标检查（达到目标价格的50%以上）
    const partialHitTarget =
      record.direction === "BUY"
        ? currentPrice >=
          record.entryPrice +
            (record.targetPrice - record.entryPrice) * partialTargetThreshold
        : currentPrice <=
          record.entryPrice -
            (record.entryPrice - record.targetPrice) * partialTargetThreshold;

    const hitStopLoss =
      record.direction === "BUY"
        ? currentPrice <= record.stopLoss
        : currentPrice >= record.stopLoss;

    // 计算准确性
    const directionCorrect = this.isDirectionCorrect(
      record.direction,
      actualDirection,
      priceChangePercent
    );
    const priceAccuracy = this.calculatePriceAccuracy(record, currentPrice);
    const overallScore = this.calculateOverallScore(
      directionCorrect,
      priceAccuracy,
      hitTarget,
      hitStopLoss,
      Math.abs(priceChangePercent),
      partialHitTarget // 新增参数
    );

    return {
      actualDirection,
      priceChange,
      priceChangePercent,
      hitTarget,
      hitStopLoss,
      directionCorrect,
      priceAccuracy,
      overallScore,
      partialHitTarget, // 新增字段
    };
  }

  private isDirectionCorrect(
    predicted: string,
    actual: string,
    priceChangePercent: number
  ): boolean {
    // 优化方向判断逻辑，考虑价格变化幅度
    if (predicted === "HOLD") {
      return actual === "SIDEWAYS";
    }

    if (predicted === "BUY") {
      // BUY预测：价格上涨或小幅下跌（可能是正常回调）
      return (
        actual === "UP" || (actual === "SIDEWAYS" && priceChangePercent >= -0.1)
      );
    }

    if (predicted === "SELL") {
      // SELL预测：价格下跌或小幅上涨（可能是正常反弹）
      return (
        actual === "DOWN" ||
        (actual === "SIDEWAYS" && priceChangePercent <= 0.1)
      );
    }

    return false;
  }

  private calculatePriceAccuracy(prediction: any, actualPrice: number): number {
    // 改进价格准确性计算，考虑目标价格和止损价格
    const targetError =
      Math.abs(actualPrice - prediction.targetPrice) / prediction.targetPrice;
    const entryError =
      Math.abs(actualPrice - prediction.entryPrice) / prediction.entryPrice;

    // 如果价格朝预期方向移动，给予更高评分
    const priceChange = actualPrice - prediction.entryPrice;
    const expectedDirection = prediction.direction === "BUY" ? 1 : -1;
    const directionBonus = priceChange * expectedDirection > 0 ? 10 : 0;

    // 基础准确性 + 方向奖励
    const baseAccuracy = Math.max(
      0,
      100 - Math.min(targetError, entryError) * 100
    );
    return Math.min(100, baseAccuracy + directionBonus);
  }

  private calculateOverallScore(
    directionCorrect: boolean,
    priceAccuracy: number,
    hitTarget: boolean,
    hitStopLoss: boolean,
    priceChangePercent: number,
    partialHitTarget: boolean = false
  ): number {
    let score = 0;

    // 方向正确性权重（降低权重，更注重实际盈利）
    if (directionCorrect) score += 40;

    // 价格准确性权重
    score += priceAccuracy * 0.2;

    // 达到目标价奖励（完全达标）
    if (hitTarget) {
      score += 30;
    } else if (partialHitTarget) {
      // 部分达标奖励（提高达标率）
      score += 20;
    }

    // 触及止损惩罚（加重惩罚）
    if (hitStopLoss) score -= 25;

    // 价格变化幅度奖励（鼓励有效的价格预测）
    const magnitudeBonus = Math.min(15, Math.abs(priceChangePercent) * 3);
    score += magnitudeBonus;

    // 极短期交易奖励（鼓励快速获利）
    if (priceChangePercent > 1.5 && !hitStopLoss) {
      score += 10; // 快速获利奖励
    }

    return Math.max(0, Math.min(100, score));
  }

  private parseTimeframe(timeframe: string): number {
    if (timeframe.includes("分钟")) {
      const minutes = parseInt(timeframe);
      return minutes * 60 * 1000;
    }
    if (timeframe.includes("小时")) {
      const hours = parseInt(timeframe);
      return hours * 60 * 60 * 1000;
    }
    return 2 * 60 * 60 * 1000; // 默认2小时
  }

  private async getStatsByField(
    records: any[],
    field: string
  ): Promise<Record<string, number>> {
    const stats: Record<string, number> = {};

    for (const record of records) {
      const key = record[field];
      if (!stats[key]) {
        stats[key] = 0;
      }
      stats[key] += record.overallScore || 0;
    }

    // 计算平均值
    Object.keys(stats).forEach((key) => {
      const count = records.filter((r) => r[field] === key).length;
      stats[key] = stats[key] / count;
    });

    return stats;
  }

  private async getStatsByConfidence(
    records: any[]
  ): Promise<Record<string, number>> {
    const stats: Record<string, number> = {};

    for (const record of records) {
      const range = this.getConfidenceRange(record.confidence);
      if (!stats[range]) {
        stats[range] = 0;
      }
      stats[range] += record.overallScore || 0;
    }

    // 计算平均值
    Object.keys(stats).forEach((key) => {
      const count = records.filter(
        (r) => this.getConfidenceRange(r.confidence) === key
      ).length;
      stats[key] = stats[key] / count;
    });

    return stats;
  }

  private getConfidenceRange(confidence: number): string {
    if (confidence >= 80) return "高信心(80-100%)";
    if (confidence >= 60) return "中信心(60-79%)";
    return "低信心(0-59%)";
  }

  private async calculateRecentTrend(records: any[]): Promise<number> {
    if (records.length < 10) return 0;

    const recent = records.slice(-10);
    const older = records.slice(-20, -10);

    if (older.length === 0) return 0;

    const recentAvg =
      recent.reduce((sum, r) => sum + (r.overallScore || 0), 0) / recent.length;
    const olderAvg =
      older.reduce((sum, r) => sum + (r.overallScore || 0), 0) / older.length;

    return recentAvg - olderAvg;
  }

  private async updateAccuracyStats(): Promise<void> {
    // 这里可以实现定期统计数据的更新逻辑
    // 暂时省略，可以后续添加
  }

  private getEmptyStats(): AccuracyStatsResult {
    return {
      totalPredictions: 0,
      verifiedPredictions: 0,
      directionAccuracy: 0,
      priceAccuracy: 0,
      overallAccuracy: 0,
      byTimeframe: {},
      bySymbol: {},
      byConfidence: {},
      recentTrend: 0,
    };
  }

  private getEmptyDetailedStats(): DetailedStatsResult {
    return {
      hitTargetRate: 0,
      hitStopLossRate: 0,
      avgProfitLoss: 0,
      bestPerformingStrategy: "无数据",
      worstPerformingStrategy: "无数据",
      marketConditionAnalysis: "数据不足",
      riskMetrics: {
        maxDrawdown: 0,
        sharpeRatio: 0,
        winRate: 0,
      },
      performanceTrends: {
        last7Days: 0,
        last30Days: 0,
        improvement: 0,
      },
      rangeValidationStats: {
        rangeHitRate: 0,
        avgTimeToTarget: 0,
        bestPerformanceInRange: 0,
        rangeVolatility: 0,
      },
    };
  }

  /**
   * 计算最大回撤
   */
  private calculateMaxDrawdown(records: any[]): number {
    if (records.length < 2) return 0;

    let maxDrawdown = 0;
    let peak = 0;
    let cumulativeReturn = 0;

    for (const record of records) {
      if (record.priceChangePercent !== null) {
        cumulativeReturn += record.priceChangePercent;
        peak = Math.max(peak, cumulativeReturn);
        const drawdown = peak - cumulativeReturn;
        maxDrawdown = Math.max(maxDrawdown, drawdown);
      }
    }

    return maxDrawdown;
  }

  /**
   * 计算夏普比率
   */
  private calculateSharpeRatio(returns: number[]): number {
    if (returns.length < 2) return 0;

    const avgReturn = returns.reduce((sum, r) => sum + r, 0) / returns.length;
    const variance =
      returns.reduce((sum, r) => sum + Math.pow(r - avgReturn, 2), 0) /
      returns.length;
    const stdDev = Math.sqrt(variance);

    return stdDev === 0 ? 0 : avgReturn / stdDev;
  }

  /**
   * 分析策略表现
   */
  private analyzeStrategies(records: any[]): { best: string; worst: string } {
    const timeframePerformance: Record<
      string,
      { total: number; count: number }
    > = {};

    records.forEach((record) => {
      if (!timeframePerformance[record.timeframe]) {
        timeframePerformance[record.timeframe] = { total: 0, count: 0 };
      }
      timeframePerformance[record.timeframe].total += record.overallScore || 0;
      timeframePerformance[record.timeframe].count += 1;
    });

    const avgPerformance = Object.entries(timeframePerformance).map(
      ([timeframe, data]) => ({
        timeframe,
        avg: data.total / data.count,
      })
    );

    avgPerformance.sort((a, b) => b.avg - a.avg);

    return {
      best: avgPerformance.length > 0 ? avgPerformance[0].timeframe : "无数据",
      worst:
        avgPerformance.length > 0
          ? avgPerformance[avgPerformance.length - 1].timeframe
          : "无数据",
    };
  }

  /**
   * 计算表现趋势
   */
  private calculatePerformanceTrends(records: any[]): {
    last7Days: number;
    last30Days: number;
    improvement: number;
  } {
    const now = new Date();
    const sevenDaysAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
    const thirtyDaysAgo = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
    const sixtyDaysAgo = new Date(now.getTime() - 60 * 24 * 60 * 60 * 1000);

    const last7Days = records.filter(
      (r) => r.verifiedAt && new Date(r.verifiedAt) >= sevenDaysAgo
    );
    const last30Days = records.filter(
      (r) => r.verifiedAt && new Date(r.verifiedAt) >= thirtyDaysAgo
    );
    const previous30Days = records.filter(
      (r) =>
        r.verifiedAt &&
        new Date(r.verifiedAt) >= sixtyDaysAgo &&
        new Date(r.verifiedAt) < thirtyDaysAgo
    );

    const avg7Days =
      last7Days.length > 0
        ? last7Days.reduce((sum, r) => sum + (r.overallScore || 0), 0) /
          last7Days.length
        : 0;

    const avg30Days =
      last30Days.length > 0
        ? last30Days.reduce((sum, r) => sum + (r.overallScore || 0), 0) /
          last30Days.length
        : 0;

    const avgPrevious30Days =
      previous30Days.length > 0
        ? previous30Days.reduce((sum, r) => sum + (r.overallScore || 0), 0) /
          previous30Days.length
        : 0;

    const improvement = avg30Days - avgPrevious30Days;

    return {
      last7Days: avg7Days,
      last30Days: avg30Days,
      improvement,
    };
  }

  /**
   * 分析市场环境
   */
  private analyzeMarketConditions(records: any[]): string {
    if (records.length === 0) return "数据不足";

    const recentRecords = records.slice(0, 10); // 最近10个预测
    const avgVolatility =
      recentRecords
        .filter((r) => r.priceChangePercent !== null)
        .reduce((sum, r) => sum + Math.abs(r.priceChangePercent!), 0) /
      recentRecords.length;

    const upTrends = recentRecords.filter(
      (r) => r.actualDirection === "UP"
    ).length;
    const downTrends = recentRecords.filter(
      (r) => r.actualDirection === "DOWN"
    ).length;

    let condition = "";
    if (avgVolatility > 5) {
      condition = "高波动";
    } else if (avgVolatility > 2) {
      condition = "中等波动";
    } else {
      condition = "低波动";
    }

    if (upTrends > downTrends * 1.5) {
      condition += "牛市";
    } else if (downTrends > upTrends * 1.5) {
      condition += "熊市";
    } else {
      condition += "震荡市";
    }

    return condition;
  }

  /**
   * 执行范围验证 - 从API获取指定时间区间的历史价格数据
   */
  private async performRangeValidation(
    record: any,
    config: { samplingInterval: number; tolerancePercent: number }
  ): Promise<RangeValidationResult> {
    // 从验证任务中获取时间区间信息
    const validationTask = await prisma.validationTask.findFirst({
      where: { predictionId: record.id },
      orderBy: { createdAt: "desc" },
    });

    if (
      !validationTask ||
      !validationTask.validationStartTime ||
      !validationTask.validationEndTime
    ) {
      throw new Error("找不到验证任务或时间区间信息");
    }

    const validationStartTime = validationTask.validationStartTime;
    const validationEndTime = validationTask.validationEndTime;

    console.log(`🔍 开始范围验证: ${record.symbol}`);
    console.log(
      `   时间区间: ${validationStartTime.toLocaleString()} - ${validationEndTime.toLocaleString()}`
    );

    // 获取历史价格数据，使用验证任务中的采样间隔
    const samplingInterval =
      validationTask.samplingInterval || config.samplingInterval || 5;
    const historicalPrices = await this.getHistoricalPrices(
      record.symbol,
      validationStartTime,
      validationEndTime,
      samplingInterval
    );

    if (historicalPrices.length === 0) {
      console.warn("⚠️ 未获取到历史价格数据，使用当前价格作为备选");
      const currentPrice = await this.getCurrentPrice(record.symbol);
      historicalPrices.push({
        timestamp: validationEndTime,
        price: currentPrice,
      });
    }

    // 处理历史价格数据
    const samples: PriceSample[] = [];
    let bestPrice = record.entryPrice;
    let worstPrice = record.entryPrice;
    let hitTargetInRange = false;
    let hitStopLossInRange = false;
    let timeToTarget: number | undefined;
    let timeToStopLoss: number | undefined;

    for (const priceData of historicalPrices) {
      const currentPrice = priceData.price;
      const currentTime = priceData.timestamp;

      const priceChangePercent =
        ((currentPrice - record.entryPrice) / record.entryPrice) * 100;

      // 检查是否达到目标或止损
      const hitTarget =
        record.direction === "BUY"
          ? currentPrice >= record.targetPrice
          : currentPrice <= record.targetPrice;

      const hitStopLoss =
        record.direction === "BUY"
          ? currentPrice <= record.stopLoss
          : currentPrice >= record.stopLoss;

      samples.push({
        timestamp: currentTime,
        price: currentPrice,
        priceChangePercent,
        hitTarget,
        hitStopLoss,
      });

      // 更新最佳/最差价格
      if (record.direction === "BUY") {
        bestPrice = Math.max(bestPrice, currentPrice);
        worstPrice = Math.min(worstPrice, currentPrice);
      } else {
        bestPrice = Math.min(bestPrice, currentPrice);
        worstPrice = Math.max(worstPrice, currentPrice);
      }

      // 记录首次达到目标或止损的时间
      if (hitTarget && !hitTargetInRange) {
        hitTargetInRange = true;
        timeToTarget =
          (currentTime.getTime() - validationStartTime.getTime()) / (1000 * 60); // 分钟
      }

      if (hitStopLoss && !hitStopLossInRange) {
        hitStopLossInRange = true;
        timeToStopLoss =
          (currentTime.getTime() - validationStartTime.getTime()) / (1000 * 60); // 分钟
      }

      // 如果已经触及止损，提前结束
      if (hitStopLoss) {
        break;
      }
    }

    // 计算平均价格和范围评分
    const avgPrice =
      samples.length > 0
        ? samples.reduce((sum, s) => sum + s.price, 0) / samples.length
        : record.entryPrice;

    const rangeScore = this.calculateRangeScore(
      record,
      samples,
      hitTargetInRange,
      hitStopLossInRange,
      bestPrice,
      worstPrice
    );

    console.log(`✅ 范围验证完成: ${record.symbol}`);
    console.log(`   数据点数: ${samples.length}`);
    console.log(`   达到目标: ${hitTargetInRange ? "是" : "否"}`);
    console.log(`   触及止损: ${hitStopLossInRange ? "是" : "否"}`);
    console.log(`   最佳价格: ${bestPrice}`);
    console.log(`   范围评分: ${rangeScore.toFixed(1)}`);

    return {
      samples,
      bestPrice,
      worstPrice,
      avgPrice,
      hitTargetInRange,
      hitStopLossInRange,
      timeToTarget,
      timeToStopLoss,
      rangeScore,
      validationStartTime,
      validationEndTime,
      actualDataPoints: samples.length,
    };
  }

  /**
   * 基于范围验证结果计算预测结果
   */
  private calculateRangeBasedResult(
    record: any,
    rangeResult: RangeValidationResult
  ) {
    const finalSample = rangeResult.samples[rangeResult.samples.length - 1];
    const finalPrice = finalSample?.price || record.entryPrice;

    const priceChange = finalPrice - record.entryPrice;
    const priceChangePercent = (priceChange / record.entryPrice) * 100;

    // 基于范围内最佳表现判断方向
    const bestPriceChange = rangeResult.bestPrice - record.entryPrice;
    const bestPriceChangePercent = (bestPriceChange / record.entryPrice) * 100;

    let actualDirection: "UP" | "DOWN" | "SIDEWAYS";
    const directionThreshold = this.getDirectionThreshold(record.timeframe);

    if (Math.abs(bestPriceChangePercent) < directionThreshold) {
      actualDirection = "SIDEWAYS";
    } else if (bestPriceChange > 0) {
      actualDirection = "UP";
    } else {
      actualDirection = "DOWN";
    }

    // 方向正确性判断（基于范围内最佳表现）
    const directionCorrect = this.isDirectionCorrect(
      record.direction,
      actualDirection,
      bestPriceChangePercent
    );

    // 价格准确性（考虑范围内表现）
    const priceAccuracy = this.calculateRangePriceAccuracy(record, rangeResult);

    return {
      actualPrice: finalPrice,
      actualDirection,
      priceChange,
      priceChangePercent,
      hitTarget: rangeResult.hitTargetInRange,
      hitStopLoss: rangeResult.hitStopLossInRange,
      partialHitTarget: this.checkPartialHitInRange(record, rangeResult),
      directionCorrect,
      priceAccuracy,
      overallScore: rangeResult.rangeScore,
    };
  }

  /**
   * 计算范围评分
   */
  private calculateRangeScore(
    record: any,
    samples: PriceSample[],
    hitTargetInRange: boolean,
    hitStopLossInRange: boolean,
    bestPrice: number,
    worstPrice: number
  ): number {
    let score = 0;

    // 基础分数：范围内达到目标
    if (hitTargetInRange) {
      score += 40;
    }

    // 惩罚：范围内触及止损
    if (hitStopLossInRange) {
      score -= 30;
    }

    // 最佳价格表现奖励
    const bestPriceChange = bestPrice - record.entryPrice;
    const bestPriceChangePercent = (bestPriceChange / record.entryPrice) * 100;

    if (record.direction === "BUY" && bestPriceChangePercent > 0) {
      score += Math.min(25, bestPriceChangePercent * 5);
    } else if (record.direction === "SELL" && bestPriceChangePercent < 0) {
      score += Math.min(25, Math.abs(bestPriceChangePercent) * 5);
    }

    // 稳定性奖励（价格波动较小）
    if (samples.length > 1) {
      const priceVariance = this.calculatePriceVariance(samples);
      const stabilityBonus = Math.max(0, 10 - priceVariance);
      score += stabilityBonus;
    }

    // 时间效率奖励（快速达到目标）
    const timeframeMs = this.parseTimeframe(record.timeframe);
    const timeframeMinutes = timeframeMs / (1000 * 60);

    if (hitTargetInRange && samples.length > 0) {
      const targetSample = samples.find((s) => s.hitTarget);
      if (targetSample) {
        const timeToTargetMinutes = samples.indexOf(targetSample) * 5; // 假设5分钟间隔
        const timeEfficiency = Math.max(
          0,
          (timeframeMinutes - timeToTargetMinutes) / timeframeMinutes
        );
        score += timeEfficiency * 15;
      }
    }

    return Math.max(0, Math.min(100, score));
  }

  /**
   * 获取方向判断阈值
   */
  private getDirectionThreshold(timeframe: string): number {
    if (timeframe.includes("15分钟") || timeframe.includes("1小时")) {
      return 0.2;
    } else if (timeframe.includes("2小时") || timeframe.includes("8小时")) {
      return 0.5;
    } else {
      return 0.8;
    }
  }

  /**
   * 计算范围内价格准确性
   */
  private calculateRangePriceAccuracy(
    record: any,
    rangeResult: RangeValidationResult
  ): number {
    const targetDistance =
      Math.abs(rangeResult.bestPrice - record.targetPrice) / record.targetPrice;
    const baseAccuracy = Math.max(0, 100 - targetDistance * 100);

    // 如果在范围内达到目标，给予额外奖励
    const rangeBonus = rangeResult.hitTargetInRange ? 20 : 0;

    return Math.min(100, baseAccuracy + rangeBonus);
  }

  /**
   * 检查范围内是否部分达标
   */
  private checkPartialHitInRange(
    record: any,
    rangeResult: RangeValidationResult
  ): boolean {
    const partialTargetThreshold = 0.5;
    const partialTarget =
      record.direction === "BUY"
        ? record.entryPrice +
          (record.targetPrice - record.entryPrice) * partialTargetThreshold
        : record.entryPrice -
          (record.entryPrice - record.targetPrice) * partialTargetThreshold;

    return rangeResult.samples.some((sample) =>
      record.direction === "BUY"
        ? sample.price >= partialTarget
        : sample.price <= partialTarget
    );
  }

  /**
   * 计算价格方差（衡量波动性）
   */
  private calculatePriceVariance(samples: PriceSample[]): number {
    if (samples.length < 2) return 0;

    const prices = samples.map((s) => s.price);
    const avgPrice = prices.reduce((sum, p) => sum + p, 0) / prices.length;
    const variance =
      prices.reduce((sum, p) => sum + Math.pow(p - avgPrice, 2), 0) /
      prices.length;

    return (Math.sqrt(variance) / avgPrice) * 100; // 返回变异系数百分比
  }

  /**
   * 计算验证时间区间
   */
  private calculateValidationTimeRange(
    timeframe: string,
    createdAt: Date
  ): {
    validationStartTime: Date;
    validationEndTime: Date;
  } {
    const timeframeMs = this.parseTimeframe(timeframe);

    // 根据时间框架确定验证开始时间
    let validationStartTime: Date;
    let validationEndTime: Date;

    if (timeframe.includes("极短期") || timeframe.includes("15分钟到2小时")) {
      // 极短期：15分钟后开始验证，2小时后结束
      validationStartTime = new Date(createdAt.getTime() + 15 * 60 * 1000); // +15分钟
      validationEndTime = new Date(createdAt.getTime() + 2 * 60 * 60 * 1000); // +2小时
    } else if (timeframe.includes("1小时到4小时")) {
      // 短期：30分钟后开始验证，4小时后结束
      validationStartTime = new Date(createdAt.getTime() + 30 * 60 * 1000); // +30分钟
      validationEndTime = new Date(createdAt.getTime() + 4 * 60 * 60 * 1000); // +4小时
    } else if (timeframe.includes("4小时到8小时")) {
      // 中期：1小时后开始验证，8小时后结束
      validationStartTime = new Date(createdAt.getTime() + 60 * 60 * 1000); // +1小时
      validationEndTime = new Date(createdAt.getTime() + 8 * 60 * 60 * 1000); // +8小时
    } else if (timeframe.includes("8小时到24小时")) {
      // 长期：2小时后开始验证，24小时后结束
      validationStartTime = new Date(createdAt.getTime() + 2 * 60 * 60 * 1000); // +2小时
      validationEndTime = new Date(createdAt.getTime() + 24 * 60 * 60 * 1000); // +24小时
    } else {
      // 单一时间框架的处理
      const timeframeMinutes = this.parseTimeframeToMinutes(timeframe);

      if (timeframeMinutes <= 60) {
        // 1小时以内：立即开始验证
        validationStartTime = new Date(createdAt.getTime());
        validationEndTime = new Date(createdAt.getTime() + timeframeMs);
      } else if (timeframeMinutes <= 240) {
        // 1-4小时：15分钟后开始验证
        validationStartTime = new Date(createdAt.getTime() + 15 * 60 * 1000);
        validationEndTime = new Date(createdAt.getTime() + timeframeMs);
      } else {
        // 4小时以上：30分钟后开始验证
        validationStartTime = new Date(createdAt.getTime() + 30 * 60 * 1000);
        validationEndTime = new Date(createdAt.getTime() + timeframeMs);
      }
    }

    return { validationStartTime, validationEndTime };
  }

  /**
   * 解析时间框架为分钟数
   */
  private parseTimeframeToMinutes(timeframe: string): number {
    if (timeframe.includes("分钟")) {
      const minutes = parseInt(timeframe);
      return minutes;
    }
    if (timeframe.includes("小时")) {
      const hours = parseInt(timeframe);
      return hours * 60;
    }
    return 120; // 默认2小时
  }

  /**
   * 计算范围验证统计
   */
  private calculateRangeValidationStats(records: any[]): {
    rangeHitRate: number;
    avgTimeToTarget: number;
    bestPerformanceInRange: number;
    rangeVolatility: number;
  } {
    const rangeValidatedRecords = records.filter((record) => {
      try {
        const predictionData = JSON.parse(record.reasoning || "{}");
        return predictionData.rangeValidationResult;
      } catch {
        return false;
      }
    });

    if (rangeValidatedRecords.length === 0) {
      return {
        rangeHitRate: 0,
        avgTimeToTarget: 0,
        bestPerformanceInRange: 0,
        rangeVolatility: 0,
      };
    }

    let rangeHitCount = 0;
    let totalTimeToTarget = 0;
    let timeToTargetCount = 0;
    let bestPerformances: number[] = [];
    let allVolatilities: number[] = [];

    rangeValidatedRecords.forEach((record) => {
      try {
        const predictionData = JSON.parse(record.reasoning || "{}");
        const rangeResult = predictionData.rangeValidationResult;

        if (rangeResult) {
          // 统计范围内达标率
          if (rangeResult.hitTargetInRange) {
            rangeHitCount++;

            // 统计达到目标的时间
            if (rangeResult.timeToTarget !== undefined) {
              totalTimeToTarget += rangeResult.timeToTarget;
              timeToTargetCount++;
            }
          }

          // 统计最佳表现
          const entryPrice = record.entryPrice;
          const bestPriceChange =
            ((rangeResult.bestPrice - entryPrice) / entryPrice) * 100;
          bestPerformances.push(Math.abs(bestPriceChange));

          // 统计波动率
          if (rangeResult.samples && rangeResult.samples.length > 1) {
            const prices = rangeResult.samples.map((s: any) => s.price);
            const avgPrice =
              prices.reduce((sum: number, p: number) => sum + p, 0) /
              prices.length;
            const variance =
              prices.reduce(
                (sum: number, p: number) => sum + Math.pow(p - avgPrice, 2),
                0
              ) / prices.length;
            const volatility = (Math.sqrt(variance) / avgPrice) * 100;
            allVolatilities.push(volatility);
          }
        }
      } catch (error) {
        console.warn("解析范围验证结果失败:", error);
      }
    });

    const rangeHitRate = (rangeHitCount / rangeValidatedRecords.length) * 100;
    const avgTimeToTarget =
      timeToTargetCount > 0 ? totalTimeToTarget / timeToTargetCount : 0;
    const bestPerformanceInRange =
      bestPerformances.length > 0
        ? bestPerformances.reduce((sum, p) => sum + p, 0) /
          bestPerformances.length
        : 0;
    const rangeVolatility =
      allVolatilities.length > 0
        ? allVolatilities.reduce((sum, v) => sum + v, 0) /
          allVolatilities.length
        : 0;

    return {
      rangeHitRate,
      avgTimeToTarget,
      bestPerformanceInRange,
      rangeVolatility,
    };
  }
}

export const serverPredictionTracker = ServerPredictionTracker.getInstance();
